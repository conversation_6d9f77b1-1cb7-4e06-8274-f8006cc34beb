# HKServices - Firebase Integration

This is a React Native Expo app for a job services platform, now integrated with Firebase for real-time data management.

## 🔥 Firebase Integration

The app has been updated to use Firebase Firestore for data storage instead of mock data. This provides:

- **Real-time data synchronization** across all devices
- **Persistent data storage** in the cloud
- **Scalable backend** without managing servers
- **Offline support** with automatic sync when online

## 🚀 Features

- **Job Listings**: Browse and search available jobs by category
- **Real-time Updates**: Jobs update instantly across all devices
- **Job Details**: View detailed job information with real-time status
- **User Profiles**: Manage posted and taken jobs
- **Job Management**: Take jobs, mark as complete, etc.

## 📋 Prerequisites

1. **Node.js** (v16 or later)
2. **Expo CLI**: `npm install -g @expo/cli`
3. **Firebase Project**: Create a project at [Firebase Console](https://console.firebase.google.com)

## 🛠️ Setup Instructions

### 1. Install Dependencies

```bash
npm install
```

### 2. Firebase Configuration

1. **Create a Firebase Project**:
   - Go to [Firebase Console](https://console.firebase.google.com)
   - Click "Create a project"
   - Follow the setup wizard

2. **Enable Firestore**:
   - In your Firebase project, go to "Firestore Database"
   - Click "Create database"
   - Choose "Start in test mode" for development

3. **Get Firebase Config**:
   - Go to Project Settings (gear icon)
   - Scroll down to "Your apps" section
   - Click "Add app" and choose "Web"
   - Copy the Firebase configuration object

4. **Set Environment Variables**:
   - Copy `.env.example` to `.env`
   - Replace the placeholder values with your Firebase config:

```bash
cp .env.example .env
```

Edit `.env` with your Firebase credentials:
```
EXPO_PUBLIC_FIREBASE_API_KEY=your-api-key-here
EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
EXPO_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=123456789
EXPO_PUBLIC_FIREBASE_APP_ID=your-app-id-here
```

### 3. Start the App

```bash
npx expo start
```

In the output, you'll find options to open the app in:

- [development build](https://docs.expo.dev/develop/development-builds/introduction/)
- [Android emulator](https://docs.expo.dev/workflow/android-studio-emulator/)
- [iOS simulator](https://docs.expo.dev/workflow/ios-simulator/)
- [Expo Go](https://expo.dev/go), a limited sandbox for trying out app development with Expo

## 📊 Data Structure

### Jobs Collection (`jobs`)
```typescript
{
  id: string;
  title: string;
  description: string;
  reward: number;
  location: string;
  status: 'open' | 'in-progress' | 'completed';
  postedBy: string;
  postedDate: string;
  category: string;
  takenBy?: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
```

### Users Collection (`users`)
```typescript
{
  id: string;
  name: string;
  email: string;
  rating: number;
  postedJobs: string[];
  takenJobs: string[];
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
```

## 🏗️ Architecture

### Service Layer
- **JobService**: Handles all job-related operations (CRUD, real-time subscriptions)
- **UserService**: Manages user data and relationships

### Custom Hooks
- **useJobs**: Manages job data fetching and state
- **useUser**: Handles user data and authentication
- **useJobRealtime**: Real-time job updates
- **useUserJobs**: User's posted and taken jobs

### Components
- **Home Screen**: Job listings with search and filtering
- **Job Details**: Individual job view with take/complete actions
- **Profile Screen**: User profile with job history

## 🔧 Key Features Implemented

### Real-time Data
- Jobs update instantly across all devices
- Real-time listeners for job status changes
- Automatic UI updates when data changes

### Error Handling
- Comprehensive error handling for all Firebase operations
- Loading states for better UX
- Retry mechanisms for failed operations

### Offline Support
- Firebase provides automatic offline support
- Data syncs when connection is restored
- Optimistic updates for better performance

## 🚀 Getting Started with Data

The app includes a demo user system. When you first run the app:

1. A demo user will be automatically created
2. You can add sample data by running the seed script (optional)
3. All data is stored in your Firebase Firestore database

## 🔐 Security Notes

**Important**: The current setup uses Firebase in "test mode" for development. For production:

1. **Set up proper Firestore security rules**
2. **Implement Firebase Authentication**
3. **Configure proper user permissions**
4. **Set up environment-specific configurations**

## 📱 Testing

To test the Firebase integration:

1. **Start the app** on multiple devices/simulators
2. **Create/update jobs** on one device
3. **Watch real-time updates** on other devices
4. **Test offline functionality** by disconnecting internet

## 🛠️ Development

### Adding New Features

1. **Create service functions** in `services/` directory
2. **Add custom hooks** in `hooks/` directory
3. **Update components** to use new hooks
4. **Add proper error handling** and loading states

### File Structure
```
├── config/
│   └── firebase.ts          # Firebase configuration
├── services/
│   ├── jobService.ts        # Job-related operations
│   └── userService.ts       # User-related operations
├── hooks/
│   ├── useJobs.ts          # Job data hooks
│   └── useUser.ts          # User data hooks
├── types/
│   └── index.ts            # TypeScript type definitions
└── app/
    ├── (tabs)/
    │   ├── index.tsx       # Home screen (job listings)
    │   └── profile.tsx     # Profile screen
    └── job/
        └── [id].tsx        # Job details screen
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.
