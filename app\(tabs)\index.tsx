import { StyleSheet, FlatList, Pressable, RefreshControl, TextInput, ActivityIndicator } from 'react-native';
import { router } from 'expo-router';
import { ThemedView } from '@/components/ThemedView';
import { ThemedText } from '@/components/ThemedText';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Job } from '@/types';
import { Theme } from '@/constants/Theme';
import { useState, useEffect, useMemo } from 'react';
import { useJobs } from '@/hooks/useJobs';

const CategoryChip = ({ label, active, onPress }: { label: string; active: boolean; onPress: () => void }) => (
  <Pressable
    style={[styles.categoryChip, active && styles.categoryChipActive]}
    onPress={onPress}>
    <ThemedText style={[styles.categoryText, active && styles.categoryTextActive]}>
      {label}
    </ThemedText>
  </Pressable>
);

const EmptyJobsList = () => (
  <ThemedView style={styles.emptyContainer}>
    <ThemedText style={styles.emptyText}>No jobs found in this category</ThemedText>
  </ThemedView>
);

const LoadingView = () => (
  <ThemedView style={styles.emptyContainer}>
    <ActivityIndicator size="large" color={Theme.colors.primary} />
    <ThemedText style={styles.emptyText}>Loading jobs...</ThemedText>
  </ThemedView>
);

const ErrorView = ({ error, onRetry }: { error: string; onRetry: () => void }) => (
  <ThemedView style={styles.emptyContainer}>
    <ThemedText style={styles.errorText}>Error: {error}</ThemedText>
    <Pressable style={styles.retryButton} onPress={onRetry}>
      <ThemedText style={styles.retryButtonText}>Retry</ThemedText>
    </Pressable>
  </ThemedView>
);

export default function HomeScreen() {
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [searchQuery, setSearchQuery] = useState('');
  const { jobs, loading, error, refetch } = useJobs();

  const categories = ['All', 'Cleaning', 'Moving', 'Delivery', 'Maintenance'];

  const onRefresh = () => {
    refetch();
  };

  // Filter jobs based on selected category and search query
  const filteredJobs = useMemo(() => {
    return jobs.filter((job: Job) => {
      const matchesCategory = selectedCategory === 'All' ? true : job.category === selectedCategory;
      const matchesSearch = searchQuery.trim() === '' ? true :
        job.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        job.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        job.location.toLowerCase().includes(searchQuery.toLowerCase());

      return matchesCategory && matchesSearch;
    });
  }, [jobs, selectedCategory, searchQuery]);

  // Format date for display
  const formatDate = (date: any) => {
    if (!date) return '';
    if (date instanceof Date) {
      return date.toLocaleDateString();
    }
    if (typeof date === 'string') {
      return new Date(date).toLocaleDateString();
    }
    return '';
  };

  const renderJobItem = ({ item }: { item: Job }) => (
    <Pressable
      style={styles.jobCard}
      onPress={() => router.push(`/job/${item.id}`)}>
      <ThemedView style={styles.jobHeader}>
        <ThemedView style={styles.categoryTag}>
          <ThemedText style={styles.categoryTagText}>{item.category}</ThemedText>
        </ThemedView>
        <ThemedText style={styles.date}>{formatDate(item.createdAt || item.postedDate)}</ThemedText>
      </ThemedView>
      
      <ThemedText style={styles.title}>{item.title}</ThemedText>
      <ThemedText style={styles.description} numberOfLines={2}>
        {item.description}
      </ThemedText>
      
      <ThemedView style={styles.jobFooter}>
        <ThemedView style={styles.locationContainer}>
          <IconSymbol name="location.fill" size={16} color={Theme.colors.subtext} />
          <ThemedText style={styles.location}>{item.location}</ThemedText>
        </ThemedView>
        <ThemedView style={styles.rewardContainer}>
          <ThemedText style={styles.rewardText}>${item.reward}</ThemedText>
        </ThemedView>
      </ThemedView>
    </Pressable>
  );

  return (
    <ThemedView style={styles.container}>
      <ThemedView style={styles.header}>
        <ThemedText style={styles.headerTitle}>Find Jobs</ThemedText>
        <ThemedText style={styles.headerSubtitle}>Discover opportunities near you</ThemedText>
      </ThemedView>

      <ThemedView style={styles.searchContainer}>
        <IconSymbol 
          name="search.fill" 
          size={20} 
          color={Theme.colors.subtext} 
          style={styles.searchIcon}
        />
        <TextInput
          style={styles.searchInput}
          placeholder="Search jobs..."
          placeholderTextColor={Theme.colors.subtext}
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        {searchQuery.length > 0 && (
          <Pressable onPress={() => setSearchQuery('')} style={styles.clearButton}>
            <IconSymbol 
              name="xmark.circle.fill" 
              size={20} 
              color={Theme.colors.subtext}
            />
          </Pressable>
        )}
      </ThemedView>

      <FlatList
        horizontal
        showsHorizontalScrollIndicator={false}
        data={categories}
        renderItem={({ item }) => (
          <CategoryChip
            label={item}
            active={selectedCategory === item}
            onPress={() => setSelectedCategory(item)}
          />
        )}
        contentContainerStyle={styles.categoriesList}
      />

      {error ? (
        <ErrorView error={error} onRetry={refetch} />
      ) : (
        <FlatList
          data={filteredJobs}
          renderItem={renderJobItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={[
            styles.jobsList,
            (filteredJobs.length === 0 || loading) && { flex: 1 }
          ]}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl refreshing={loading} onRefresh={onRefresh} />
          }
          ListEmptyComponent={loading ? LoadingView : EmptyJobsList}
        />
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Theme.colors.background,
  },
  header: {
    padding: Theme.spacing.md,
    backgroundColor: Theme.colors.card,
    borderBottomWidth: 1,
    borderBottomColor: Theme.colors.border,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Theme.colors.text,
  },
  headerSubtitle: {
    fontSize: 16,
    color: Theme.colors.subtext,
    marginTop: Theme.spacing.xs,
  },
  categoriesList: {
    padding: Theme.spacing.md,
    gap: Theme.spacing.sm,
  },
  categoryChip: {
    paddingHorizontal: Theme.spacing.md,
    paddingVertical: Theme.spacing.sm,
    borderRadius: Theme.borderRadius.lg,
    backgroundColor: Theme.colors.card,
    marginRight: Theme.spacing.sm,
    ...Theme.shadows.sm,
  },
  categoryChipActive: {
    backgroundColor: Theme.colors.primary,
  },
  categoryText: {
    color: Theme.colors.text,
    fontWeight: '500',
  },
  categoryTextActive: {
    color: Theme.colors.card,
  },
  jobsList: {
    padding: Theme.spacing.md,
    gap: Theme.spacing.md,
  },
  jobCard: {
    backgroundColor: Theme.colors.card,
    borderRadius: Theme.borderRadius.lg,
    padding: Theme.spacing.md,
    ...Theme.shadows.md,
  },
  jobHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Theme.spacing.sm,
  },
  categoryTag: {
    backgroundColor: Theme.colors.background,
    paddingHorizontal: Theme.spacing.sm,
    paddingVertical: Theme.spacing.xs,
    borderRadius: Theme.borderRadius.sm,
  },
  categoryTagText: {
    color: Theme.colors.primary,
    fontSize: 12,
    fontWeight: '500',
  },
  date: {
    color: Theme.colors.subtext,
    fontSize: 12,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Theme.colors.text,
    marginBottom: Theme.spacing.xs,
  },
  description: {
    color: Theme.colors.subtext,
    fontSize: 14,
    lineHeight: 20,
  },
  jobFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: Theme.spacing.md,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Theme.spacing.xs,
  },
  location: {
    color: Theme.colors.subtext,
    fontSize: 14,
  },
  rewardContainer: {
    backgroundColor: Theme.colors.primary,
    paddingHorizontal: Theme.spacing.md,
    paddingVertical: Theme.spacing.xs,
    borderRadius: Theme.borderRadius.lg,
  },
  rewardText: {
    color: Theme.colors.card,
    fontWeight: 'bold',
    fontSize: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Theme.spacing.xl,
  },
  emptyText: {
    color: Theme.colors.subtext,
    fontSize: 16,
    textAlign: 'center',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Theme.colors.card,
    borderRadius: 12,
    marginHorizontal: Theme.spacing.lg,
    marginBottom: Theme.spacing.md,
    paddingHorizontal: Theme.spacing.md,
    height: 44,
  },
  searchIcon: {
    marginRight: Theme.spacing.sm,
  },
  searchInput: {
    flex: 1,
    height: '100%',
    color: Theme.colors.text,
    fontSize: 16,
  },
  clearButton: {
    padding: Theme.spacing.xs,
  },
  errorText: {
    color: Theme.colors.error,
    fontSize: 16,
    textAlign: 'center',
    marginBottom: Theme.spacing.md,
  },
  retryButton: {
    backgroundColor: Theme.colors.primary,
    paddingHorizontal: Theme.spacing.lg,
    paddingVertical: Theme.spacing.sm,
    borderRadius: Theme.borderRadius.lg,
  },
  retryButtonText: {
    color: Theme.colors.card,
    fontSize: 16,
    fontWeight: 'bold',
  },
});





