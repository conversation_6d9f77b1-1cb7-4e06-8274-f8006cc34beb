import { useState } from 'react';
import { StyleSheet, TextInput, ScrollView, Pressable, View } from 'react-native';
import { ThemedView } from '@/components/ThemedView';
import { ThemedText } from '@/components/ThemedText';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Theme } from '@/constants/Theme';

const categories = ['Cleaning', 'Moving', 'Delivery', 'Maintenance', 'Other'];

export default function PostJobScreen() {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [reward, setReward] = useState('');
  const [location, setLocation] = useState('');
  const [category, setCategory] = useState('');

  const handlePost = () => {
    // TODO: Implement job posting logic
    console.log({ title, description, reward, location, category });
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <ThemedView style={styles.content}>
        <ThemedView style={styles.header}>
          <ThemedText style={styles.headerTitle}>Post a New Job</ThemedText>
          <ThemedText style={styles.headerSubtitle}>
            Fill in the details to create your job posting
          </ThemedText>
        </ThemedView>

        <ThemedView style={styles.form}>
          <ThemedView style={styles.inputGroup}>
            <ThemedText style={styles.label}>Category</ThemedText>
            <View style={styles.categoryContainer}>
              {categories.map((cat) => (
                <Pressable
                  key={cat}
                  style={[
                    styles.categoryChip,
                    category === cat && styles.categoryChipSelected,
                  ]}
                  onPress={() => setCategory(cat)}>
                  <ThemedText
                    style={[
                      styles.categoryText,
                      category === cat && styles.categoryTextSelected,
                    ]}>
                    {cat}
                  </ThemedText>
                </Pressable>
              ))}
            </View>
          </ThemedView>

          <ThemedView style={styles.inputGroup}>
            <ThemedText style={styles.label}>Title</ThemedText>
            <TextInput
              style={styles.input}
              value={title}
              onChangeText={setTitle}
              placeholder="What do you need help with?"
              placeholderTextColor={Theme.colors.subtext}
            />
          </ThemedView>

          <ThemedView style={styles.inputGroup}>
            <ThemedText style={styles.label}>Description</ThemedText>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={description}
              onChangeText={setDescription}
              placeholder="Provide detailed information about the job"
              multiline
              numberOfLines={4}
              placeholderTextColor={Theme.colors.subtext}
            />
          </ThemedView>

          <ThemedView style={styles.row}>
            <ThemedView style={[styles.inputGroup, { flex: 1 }]}>
              <ThemedText style={styles.label}>Reward ($)</ThemedText>
              <TextInput
                style={styles.input}
                value={reward}
                onChangeText={setReward}
                placeholder="Amount"
                keyboardType="numeric"
                placeholderTextColor={Theme.colors.subtext}
              />
            </ThemedView>

            <ThemedView style={[styles.inputGroup, { flex: 2 }]}>
              <ThemedText style={styles.label}>Location</ThemedText>
              <TextInput
                style={styles.input}
                value={location}
                onChangeText={setLocation}
                placeholder="Where is the job located?"
                placeholderTextColor={Theme.colors.subtext}
              />
            </ThemedView>
          </ThemedView>
        </ThemedView>

        <Pressable style={styles.button} onPress={handlePost}>
          <IconSymbol name="paperplane.fill" size={20} color={Theme.colors.card} />
          <ThemedText style={styles.buttonText}>Post Job</ThemedText>
        </Pressable>
      </ThemedView>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Theme.colors.background,
  },
  content: {
    padding: Theme.spacing.md,
  },
  header: {
    marginBottom: Theme.spacing.xl,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Theme.colors.text,
  },
  headerSubtitle: {
    fontSize: 16,
    color: Theme.colors.subtext,
    marginTop: Theme.spacing.xs,
  },
  form: {
    gap: Theme.spacing.lg,
  },
  inputGroup: {
    gap: Theme.spacing.sm,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: Theme.colors.text,
  },
  input: {
    backgroundColor: Theme.colors.card,
    padding: Theme.spacing.md,
    borderRadius: Theme.borderRadius.md,
    fontSize: 16,
    color: Theme.colors.text,
    borderWidth: 1,
    borderColor: Theme.colors.border,
  },
  textArea: {
    height: 120,
    textAlignVertical: 'top',
  },
  row: {
    flexDirection: 'row',
    gap: Theme.spacing.md,
  },
  categoryContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Theme.spacing.sm,
  },
  categoryChip: {
    paddingHorizontal: Theme.spacing.md,
    paddingVertical: Theme.spacing.sm,
    borderRadius: Theme.borderRadius.lg,
    backgroundColor: Theme.colors.card,
    borderWidth: 1,
    borderColor: Theme.colors.border,
  },
  categoryChipSelected: {
    backgroundColor: Theme.colors.primary,
    borderColor: Theme.colors.primary,
  },
  categoryText: {
    color: Theme.colors.text,
    fontWeight: '500',
  },
  categoryTextSelected: {
    color: Theme.colors.card,
  },
  button: {
    backgroundColor: Theme.colors.primary,
    padding: Theme.spacing.md,
    borderRadius: Theme.borderRadius.lg,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: Theme.spacing.sm,
    marginTop: Theme.spacing.xl,
  },
  buttonText: {
    color: Theme.colors.card,
    fontSize: 18,
    fontWeight: 'bold',
  },
});
