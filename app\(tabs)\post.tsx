import { useState } from 'react';
import { StyleSheet, TextInput, ScrollView, Pressable, View, Alert, ActivityIndicator } from 'react-native';
import { router } from 'expo-router';
import { ThemedView } from '@/components/ThemedView';
import { ThemedText } from '@/components/ThemedText';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Theme } from '@/constants/Theme';
import { useUser } from '@/hooks/useUser';
import { usePostJob } from '@/hooks/usePostJob';
import { PostJobTest } from '@/components/PostJobTest';
import { AuthGuard } from '@/components/AuthGuard';

const categories = ['Cleaning', 'Moving', 'Delivery', 'Maintenance', 'Other'];

export default function PostJobScreen() {
  return (
    <AuthGuard>
      <PostJobContent />
    </AuthGuard>
  );
}

function PostJobContent() {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [reward, setReward] = useState('');
  const [location, setLocation] = useState('');
  const [category, setCategory] = useState('');

  const { user } = useUser();
  const { postJob, isLoading } = usePostJob();

  // Form validation
  const validateForm = () => {
    if (!title.trim()) {
      Alert.alert('Validation Error', 'Please enter a job title');
      return false;
    }
    if (title.length > 100) {
      Alert.alert('Validation Error', 'Job title must be 100 characters or less');
      return false;
    }
    if (!description.trim()) {
      Alert.alert('Validation Error', 'Please enter a job description');
      return false;
    }
    if (description.length > 500) {
      Alert.alert('Validation Error', 'Job description must be 500 characters or less');
      return false;
    }
    if (!category) {
      Alert.alert('Validation Error', 'Please select a category');
      return false;
    }
    if (!location.trim()) {
      Alert.alert('Validation Error', 'Please enter a location');
      return false;
    }
    if (!reward.trim() || isNaN(Number(reward)) || Number(reward) <= 0) {
      Alert.alert('Validation Error', 'Please enter a valid reward amount');
      return false;
    }
    if (Number(reward) > 10000) {
      Alert.alert('Validation Error', 'Reward amount cannot exceed $10,000');
      return false;
    }
    if (!user) {
      Alert.alert('Error', 'User not found. Please try again.');
      return false;
    }
    return true;
  };

  const resetForm = () => {
    setTitle('');
    setDescription('');
    setReward('');
    setLocation('');
    setCategory('');
  };

  const handlePost = async () => {
    if (!validateForm()) return;

    try {
      // Create job data
      const jobData = {
        title: title.trim(),
        description: description.trim(),
        reward: Number(reward),
        location: location.trim(),
        category: category,
      };

      // Post the job using the hook
      const jobId = await postJob(jobData);

      if (jobId) {
        // Show success message
        Alert.alert(
          'Success! 🎉',
          'Your job has been posted successfully!',
          [
            {
              text: 'Post Another',
              onPress: resetForm,
            },
            {
              text: 'View Jobs',
              onPress: () => {
                resetForm();
                router.push('/(tabs)');
              },
            },
          ]
        );
      }

    } catch (error) {
      console.error('❌ Error posting job:', error);
      Alert.alert(
        'Error',
        'Failed to post job. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <ThemedView style={styles.content}>
        <ThemedView style={styles.header}>
          <ThemedText style={styles.headerTitle}>Post a New Job</ThemedText>
          <ThemedText style={styles.headerSubtitle}>
            Fill in the details to create your job posting
          </ThemedText>
        </ThemedView>

        <ThemedView style={styles.form}>
          <ThemedView style={styles.inputGroup}>
            <ThemedText style={styles.label}>Category</ThemedText>
            <View style={styles.categoryContainer}>
              {categories.map((cat) => (
                <Pressable
                  key={cat}
                  style={[
                    styles.categoryChip,
                    category === cat && styles.categoryChipSelected,
                  ]}
                  onPress={() => setCategory(cat)}>
                  <ThemedText
                    style={[
                      styles.categoryText,
                      category === cat && styles.categoryTextSelected,
                    ]}>
                    {cat}
                  </ThemedText>
                </Pressable>
              ))}
            </View>
          </ThemedView>

          <ThemedView style={styles.inputGroup}>
            <ThemedView style={styles.labelRow}>
              <ThemedText style={styles.label}>Title</ThemedText>
              <ThemedText style={styles.charCount}>{title.length}/100</ThemedText>
            </ThemedView>
            <TextInput
              style={[styles.input, title.length > 100 && styles.inputError]}
              value={title}
              onChangeText={setTitle}
              placeholder="What do you need help with?"
              placeholderTextColor={Theme.colors.subtext}
              maxLength={100}
            />
          </ThemedView>

          <ThemedView style={styles.inputGroup}>
            <ThemedView style={styles.labelRow}>
              <ThemedText style={styles.label}>Description</ThemedText>
              <ThemedText style={styles.charCount}>{description.length}/500</ThemedText>
            </ThemedView>
            <TextInput
              style={[styles.input, styles.textArea, description.length > 500 && styles.inputError]}
              value={description}
              onChangeText={setDescription}
              placeholder="Provide detailed information about the job"
              multiline
              numberOfLines={4}
              placeholderTextColor={Theme.colors.subtext}
              maxLength={500}
            />
          </ThemedView>

          <ThemedView style={styles.row}>
            <ThemedView style={[styles.inputGroup, { flex: 1 }]}>
              <ThemedText style={styles.label}>Reward ($)</ThemedText>
              <TextInput
                style={styles.input}
                value={reward}
                onChangeText={setReward}
                placeholder="Amount"
                keyboardType="numeric"
                placeholderTextColor={Theme.colors.subtext}
              />
            </ThemedView>

            <ThemedView style={[styles.inputGroup, { flex: 2 }]}>
              <ThemedText style={styles.label}>Location</ThemedText>
              <TextInput
                style={styles.input}
                value={location}
                onChangeText={setLocation}
                placeholder="Where is the job located?"
                placeholderTextColor={Theme.colors.subtext}
              />
            </ThemedView>
          </ThemedView>
        </ThemedView>

        <Pressable
          style={[styles.button, isLoading && styles.buttonDisabled]}
          onPress={handlePost}
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <ActivityIndicator size="small" color={Theme.colors.card} />
              <ThemedText style={styles.buttonText}>Posting...</ThemedText>
            </>
          ) : (
            <>
              <IconSymbol name="paperplane.fill" size={20} color={Theme.colors.card} />
              <ThemedText style={styles.buttonText}>Post Job</ThemedText>
            </>
          )}
        </Pressable>
      </ThemedView>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Theme.colors.background,
  },
  content: {
    padding: Theme.spacing.md,
  },
  header: {
    marginBottom: Theme.spacing.xl,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Theme.colors.text,
  },
  headerSubtitle: {
    fontSize: 16,
    color: Theme.colors.subtext,
    marginTop: Theme.spacing.xs,
  },
  form: {
    gap: Theme.spacing.lg,
  },
  inputGroup: {
    gap: Theme.spacing.sm,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: Theme.colors.text,
  },
  input: {
    backgroundColor: Theme.colors.card,
    padding: Theme.spacing.md,
    borderRadius: Theme.borderRadius.md,
    fontSize: 16,
    color: Theme.colors.text,
    borderWidth: 1,
    borderColor: Theme.colors.border,
  },
  textArea: {
    height: 120,
    textAlignVertical: 'top',
  },
  row: {
    flexDirection: 'row',
    gap: Theme.spacing.md,
  },
  categoryContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Theme.spacing.sm,
  },
  categoryChip: {
    paddingHorizontal: Theme.spacing.md,
    paddingVertical: Theme.spacing.sm,
    borderRadius: Theme.borderRadius.lg,
    backgroundColor: Theme.colors.card,
    borderWidth: 1,
    borderColor: Theme.colors.border,
  },
  categoryChipSelected: {
    backgroundColor: Theme.colors.primary,
    borderColor: Theme.colors.primary,
  },
  categoryText: {
    color: Theme.colors.text,
    fontWeight: '500',
  },
  categoryTextSelected: {
    color: Theme.colors.card,
  },
  button: {
    backgroundColor: Theme.colors.primary,
    padding: Theme.spacing.md,
    borderRadius: Theme.borderRadius.lg,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: Theme.spacing.sm,
    marginTop: Theme.spacing.xl,
  },
  buttonText: {
    color: Theme.colors.card,
    fontSize: 18,
    fontWeight: 'bold',
  },
  buttonDisabled: {
    backgroundColor: Theme.colors.subtext,
    opacity: 0.7,
  },
  labelRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Theme.spacing.xs,
  },
  charCount: {
    fontSize: 12,
    color: Theme.colors.subtext,
  },
  inputError: {
    borderColor: Theme.colors.error,
    borderWidth: 1,
  },
});
