import { useState } from 'react';
import { StyleSheet, FlatList, Pressable, RefreshControl, ActivityIndicator } from 'react-native';
import { router } from 'expo-router';
import { ThemedView } from '@/components/ThemedView';
import { ThemedText } from '@/components/ThemedText';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Theme } from '@/constants/Theme';
import { Job } from '@/types';
import { useUser, useUserJobs, useUserStats } from '@/hooks/useUser';
import { useAuthContext } from '@/contexts/AuthContext';
import { AuthGuard } from '@/components/AuthGuard';
import { useLogoutNavigation } from '@/hooks/useAuthNavigation';
import { LogoutConfirmation } from '@/components/LogoutConfirmation';

export default function ProfileScreen() {
  return (
    <AuthGuard>
      <ProfileContent />
    </AuthGuard>
  );
}

function ProfileContent() {
  const [activeTab, setActiveTab] = useState<'posted' | 'taken'>('posted');
  const [showLogoutConfirmation, setShowLogoutConfirmation] = useState(false);
  const { user, loading: userLoading } = useUser();
  const { postedJobs, takenJobs, loading: jobsLoading, refetch: refetchJobs } = useUserJobs();
  const { stats, loading: statsLoading } = useUserStats();


  // Handle logout navigation
  useLogoutNavigation();

  const onRefresh = () => {
    refetchJobs();
  };

  const handleLogout = () => {
    setShowLogoutConfirmation(true);
  };

  const handleCancelLogout = () => {
    setShowLogoutConfirmation(false);
  };

  const currentJobs = activeTab === 'posted' ? postedJobs : takenJobs;
  const isLoading = userLoading || jobsLoading || statsLoading;

  const renderJobItem = ({ item }: { item: Job }) => (
    <Pressable
      style={styles.jobCard}
      onPress={() => router.push(`/job/${item.id}`)}>
      <ThemedView style={styles.jobHeader}>
        <ThemedView style={styles.categoryTag}>
          <ThemedText style={styles.categoryTagText}>{item.category}</ThemedText>
        </ThemedView>
        <ThemedText style={styles.statusTag}>{item.status}</ThemedText>
      </ThemedView>
      
      <ThemedText style={styles.jobTitle}>{item.title}</ThemedText>
      <ThemedText style={styles.jobDescription} numberOfLines={2}>
        {item.description}
      </ThemedText>
      
      <ThemedView style={styles.jobFooter}>
        <ThemedView style={styles.locationContainer}>
          <IconSymbol name="location.fill" size={16} color={Theme.colors.subtext} />
          <ThemedText style={styles.location}>{item.location}</ThemedText>
        </ThemedView>
        <ThemedView style={styles.reward}>
          <ThemedText style={styles.rewardText}>${item.reward}</ThemedText>
        </ThemedView>
      </ThemedView>
    </Pressable>
  );

  return (
    <ThemedView style={styles.container}>
      <ThemedView style={styles.header}>
        <ThemedView style={styles.headerTop}>
          <ThemedView style={styles.profileInfo}>
            <ThemedView style={styles.avatarContainer}>
              <IconSymbol name="person.fill" size={40} color={Theme.colors.primary} />
            </ThemedView>
            <ThemedText style={styles.name}>{user?.name || 'Loading...'}</ThemedText>
            <ThemedView style={styles.ratingContainer}>
              <IconSymbol name="star.fill" size={16} color={Theme.colors.warning} />
              <ThemedText style={styles.rating}>{user?.rating || 0}/5</ThemedText>
            </ThemedView>
          </ThemedView>

          <Pressable style={styles.logoutButton} onPress={handleLogout}>
            <IconSymbol name="xmark.circle.fill" size={16} color={Theme.colors.error} />
            <ThemedText style={styles.logoutButtonText}>Logout</ThemedText>
          </Pressable>
        </ThemedView>

        <ThemedView style={styles.stats}>
          <ThemedView style={styles.statItem}>
            <ThemedText style={styles.statValue}>{stats.jobsPosted}</ThemedText>
            <ThemedText style={styles.statLabel}>Jobs Posted</ThemedText>
          </ThemedView>
          <ThemedView style={styles.statDivider} />
          <ThemedView style={styles.statItem}>
            <ThemedText style={styles.statValue}>{stats.jobsTaken}</ThemedText>
            <ThemedText style={styles.statLabel}>Jobs Taken</ThemedText>
          </ThemedView>
        </ThemedView>
      </ThemedView>

      {/* Settings Section */}
      <ThemedView style={styles.settingsSection}>
        <Pressable style={styles.settingsItem} onPress={handleLogout}>
          <ThemedView style={styles.settingsItemLeft}>
            <IconSymbol name="xmark.circle.fill" size={20} color={Theme.colors.error} />
            <ThemedText style={styles.settingsItemText}>Logout</ThemedText>
          </ThemedView>
          <IconSymbol name="chevron.right" size={16} color={Theme.colors.subtext} />
        </Pressable>
      </ThemedView>

      <ThemedView style={styles.tabContainer}>
        <Pressable
          style={[styles.tab, activeTab === 'posted' && styles.activeTab]}
          onPress={() => setActiveTab('posted')}>
          <ThemedText style={[styles.tabText, activeTab === 'posted' && styles.activeTabText]}>
            Posted Jobs
          </ThemedText>
        </Pressable>
        <Pressable
          style={[styles.tab, activeTab === 'taken' && styles.activeTab]}
          onPress={() => setActiveTab('taken')}>
          <ThemedText style={[styles.tabText, activeTab === 'taken' && styles.activeTabText]}>
            Taken Jobs
          </ThemedText>
        </Pressable>
      </ThemedView>

      {isLoading ? (
        <ThemedView style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Theme.colors.primary} />
          <ThemedText style={styles.loadingText}>Loading jobs...</ThemedText>
        </ThemedView>
      ) : (
        <FlatList
          data={currentJobs}
          renderItem={renderJobItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={[
            styles.jobsList,
            currentJobs.length === 0 && { flex: 1, justifyContent: 'center' }
          ]}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl refreshing={jobsLoading} onRefresh={onRefresh} />
          }
          ListEmptyComponent={() => (
            <ThemedView style={styles.emptyContainer}>
              <ThemedText style={styles.emptyText}>
                No {activeTab} jobs found
              </ThemedText>
            </ThemedView>
          )}
        />
      )}

      {/* Logout Confirmation Modal */}
      {showLogoutConfirmation && (
        <LogoutConfirmation onCancel={handleCancelLogout} />
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Theme.colors.background,
  },
  header: {
    backgroundColor: Theme.colors.card,
    padding: Theme.spacing.lg,
    ...Theme.shadows.md,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  profileInfo: {
    alignItems: 'center',
    gap: Theme.spacing.sm,
    flex: 1,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Theme.spacing.xs,
    padding: Theme.spacing.sm,
    borderRadius: Theme.borderRadius.md,
    backgroundColor: Theme.colors.background,
    borderWidth: 1,
    borderColor: Theme.colors.error,
  },
  logoutButtonText: {
    color: Theme.colors.error,
    fontSize: 12,
    fontWeight: '500',
  },
  avatarContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: Theme.colors.background,
    alignItems: 'center',
    justifyContent: 'center',
    ...Theme.shadows.sm,
  },
  name: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Theme.colors.text,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Theme.spacing.xs,
  },
  rating: {
    fontSize: 16,
    color: Theme.colors.text,
  },
  stats: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: Theme.spacing.lg,
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statDivider: {
    width: 1,
    height: '100%',
    backgroundColor: Theme.colors.border,
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Theme.colors.primary,
  },
  statLabel: {
    fontSize: 14,
    color: Theme.colors.subtext,
  },
  tabContainer: {
    flexDirection: 'row',
    padding: Theme.spacing.md,
    gap: Theme.spacing.md,
  },
  tab: {
    flex: 1,
    paddingVertical: Theme.spacing.sm,
    alignItems: 'center',
    borderRadius: Theme.borderRadius.lg,
    backgroundColor: Theme.colors.card,
    ...Theme.shadows.sm,
  },
  activeTab: {
    backgroundColor: Theme.colors.primary,
  },
  tabText: {
    fontSize: 16,
    fontWeight: '500',
    color: Theme.colors.text,
  },
  activeTabText: {
    color: Theme.colors.card,
  },
  jobsList: {
    padding: Theme.spacing.md,
    gap: Theme.spacing.md,
  },
  jobCard: {
    backgroundColor: Theme.colors.card,
    borderRadius: Theme.borderRadius.lg,
    padding: Theme.spacing.md,
    ...Theme.shadows.md,
  },
  jobHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Theme.spacing.sm,
  },
  categoryTag: {
    backgroundColor: Theme.colors.background,
    paddingHorizontal: Theme.spacing.sm,
    paddingVertical: Theme.spacing.xs,
    borderRadius: Theme.borderRadius.sm,
  },
  categoryTagText: {
    color: Theme.colors.primary,
    fontSize: 12,
    fontWeight: '500',
  },
  statusTag: {
    fontSize: 12,
    color: Theme.colors.subtext,
    textTransform: 'capitalize',
  },
  jobTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Theme.colors.text,
    marginBottom: Theme.spacing.xs,
  },
  jobDescription: {
    color: Theme.colors.subtext,
    fontSize: 14,
    lineHeight: 20,
  },
  jobFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: Theme.spacing.md,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Theme.spacing.xs,
  },
  location: {
    color: Theme.colors.subtext,
    fontSize: 14,
  },
  reward: {
    backgroundColor: Theme.colors.primary,
    paddingHorizontal: Theme.spacing.md,
    paddingVertical: Theme.spacing.xs,
    borderRadius: Theme.borderRadius.lg,
  },
  rewardText: {
    color: Theme.colors.card,
    fontWeight: 'bold',
    fontSize: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Theme.spacing.xl,
  },
  loadingText: {
    marginTop: Theme.spacing.md,
    fontSize: 16,
    color: Theme.colors.subtext,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Theme.spacing.xl,
  },
  emptyText: {
    fontSize: 16,
    color: Theme.colors.subtext,
    textAlign: 'center',
  },
  settingsSection: {
    backgroundColor: Theme.colors.card,
    marginHorizontal: Theme.spacing.md,
    marginVertical: Theme.spacing.sm,
    borderRadius: Theme.borderRadius.lg,
    ...Theme.shadows.sm,
  },
  settingsItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: Theme.spacing.md,
  },
  settingsItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Theme.spacing.md,
  },
  settingsItemText: {
    fontSize: 16,
    color: Theme.colors.error,
    fontWeight: '500',
  },
});
