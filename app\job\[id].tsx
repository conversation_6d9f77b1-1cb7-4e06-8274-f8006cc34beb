import { useLocalSearchParams } from 'expo-router';
import { StyleSheet, ScrollView, Pressable, ActivityIndicator, Alert } from 'react-native';
import { ThemedView } from '@/components/ThemedView';
import { ThemedText } from '@/components/ThemedText';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Theme } from '@/constants/Theme';
import { Job } from '@/types';
import { useJobRealtime } from '@/hooks/useJobs';
import { useUser } from '@/hooks/useUser';
import { JobService } from '@/services/jobService';
import { UserService } from '@/services/userService';

export default function JobDetailsScreen() {
  const { id } = useLocalSearchParams();
  const jobId = Array.isArray(id) ? id[0] : id;
  const { job, loading, error } = useJobRealtime(jobId || '');
  const { user } = useUser();

  const handleTakeJob = async () => {
    if (!job || !user) return;

    try {
      await JobService.takeJob(job.id, user.id);
      await UserService.addTakenJob(user.id, job.id);
      Alert.alert('Success', 'Job taken successfully!');
    } catch (error) {
      Alert.alert('Error', 'Failed to take job. Please try again.');
    }
  };

  const formatDate = (date: any) => {
    if (!date) return '';
    if (date instanceof Date) {
      return date.toLocaleDateString();
    }
    if (typeof date === 'string') {
      return new Date(date).toLocaleDateString();
    }
    return '';
  };

  if (loading) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Theme.colors.primary} />
        <ThemedText style={styles.loadingText}>Loading job details...</ThemedText>
      </ThemedView>
    );
  }

  if (error || !job) {
    return (
      <ThemedView style={styles.errorContainer}>
        <ThemedText style={styles.errorText}>
          {error || 'Job not found'}
        </ThemedText>
      </ThemedView>
    );
  }

  const canTakeJob = job.status === 'open' && job.postedBy !== user?.id;

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <ThemedView style={styles.content}>
        <ThemedView style={styles.header}>
          <ThemedView style={styles.categoryTag}>
            <ThemedText style={styles.categoryTagText}>{job.category}</ThemedText>
          </ThemedView>
          <ThemedText style={styles.date}>
            Posted on {formatDate(job.createdAt || job.postedDate)}
          </ThemedText>
        </ThemedView>

        <ThemedText style={styles.title}>{job.title}</ThemedText>

        <ThemedView style={styles.rewardCard}>
          <ThemedView style={styles.rewardInfo}>
            <ThemedText style={styles.rewardLabel}>Reward</ThemedText>
            <ThemedText style={styles.rewardAmount}>${job.reward}</ThemedText>
          </ThemedView>
          {canTakeJob && (
            <Pressable style={styles.takeJobButton} onPress={handleTakeJob}>
              <ThemedText style={styles.buttonText}>Take Job</ThemedText>
            </Pressable>
          )}
          {job.status !== 'open' && (
            <ThemedView style={styles.statusBadge}>
              <ThemedText style={styles.statusText}>{job.status.toUpperCase()}</ThemedText>
            </ThemedView>
          )}
        </ThemedView>

        <ThemedView style={styles.section}>
          <ThemedText style={styles.sectionTitle}>Location</ThemedText>
          <ThemedView style={styles.locationContainer}>
            <IconSymbol name="location.fill" size={20} color={Theme.colors.primary} />
            <ThemedText style={styles.location}>{job.location}</ThemedText>
          </ThemedView>
        </ThemedView>

        <ThemedView style={styles.section}>
          <ThemedText style={styles.sectionTitle}>Description</ThemedText>
          <ThemedText style={styles.description}>{job.description}</ThemedText>
        </ThemedView>

        <ThemedView style={styles.section}>
          <ThemedText style={styles.sectionTitle}>Posted By</ThemedText>
          <ThemedView style={styles.userCard}>
            <ThemedView style={styles.avatarContainer}>
              <IconSymbol name="person.fill" size={24} color={Theme.colors.primary} />
            </ThemedView>
            <ThemedView>
              <ThemedText style={styles.userName}>User {job.postedBy}</ThemedText>
              <ThemedView style={styles.ratingContainer}>
                <IconSymbol name="star.fill" size={16} color={Theme.colors.warning} />
                <ThemedText style={styles.rating}>4.5/5</ThemedText>
              </ThemedView>
            </ThemedView>
          </ThemedView>
        </ThemedView>
      </ThemedView>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Theme.colors.background,
  },
  content: {
    padding: Theme.spacing.lg,
    gap: Theme.spacing.lg,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  categoryTag: {
    backgroundColor: Theme.colors.background,
    paddingHorizontal: Theme.spacing.sm,
    paddingVertical: Theme.spacing.xs,
    borderRadius: Theme.borderRadius.sm,
  },
  categoryTagText: {
    color: Theme.colors.primary,
    fontSize: 14,
    fontWeight: '500',
  },
  date: {
    fontSize: 14,
    color: Theme.colors.subtext,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Theme.colors.text,
  },
  rewardCard: {
    backgroundColor: Theme.colors.card,
    borderRadius: Theme.borderRadius.lg,
    padding: Theme.spacing.lg,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    ...Theme.shadows.md,
  },
  rewardInfo: {
    gap: Theme.spacing.xs,
  },
  rewardLabel: {
    fontSize: 14,
    color: Theme.colors.subtext,
  },
  rewardAmount: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Theme.colors.text,
  },
  takeJobButton: {
    backgroundColor: Theme.colors.primary,
    paddingHorizontal: Theme.spacing.lg,
    paddingVertical: Theme.spacing.md,
    borderRadius: Theme.borderRadius.lg,
  },
  buttonText: {
    color: Theme.colors.card,
    fontSize: 16,
    fontWeight: 'bold',
  },
  section: {
    gap: Theme.spacing.sm,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Theme.colors.text,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Theme.spacing.sm,
  },
  location: {
    fontSize: 16,
    color: Theme.colors.text,
  },
  description: {
    fontSize: 16,
    color: Theme.colors.text,
    lineHeight: 24,
  },
  userCard: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Theme.spacing.md,
    backgroundColor: Theme.colors.card,
    padding: Theme.spacing.md,
    borderRadius: Theme.borderRadius.lg,
    ...Theme.shadows.sm,
  },
  avatarContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: Theme.colors.background,
    alignItems: 'center',
    justifyContent: 'center',
  },
  userName: {
    fontSize: 16,
    fontWeight: '500',
    color: Theme.colors.text,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Theme.spacing.xs,
  },
  rating: {
    fontSize: 14,
    color: Theme.colors.subtext,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Theme.colors.background,
    padding: Theme.spacing.xl,
  },
  loadingText: {
    marginTop: Theme.spacing.md,
    fontSize: 16,
    color: Theme.colors.subtext,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Theme.colors.background,
    padding: Theme.spacing.xl,
  },
  errorText: {
    fontSize: 16,
    color: Theme.colors.error,
    textAlign: 'center',
  },
  statusBadge: {
    backgroundColor: Theme.colors.warning,
    paddingHorizontal: Theme.spacing.md,
    paddingVertical: Theme.spacing.xs,
    borderRadius: Theme.borderRadius.lg,
  },
  statusText: {
    color: Theme.colors.card,
    fontSize: 14,
    fontWeight: 'bold',
  },
});