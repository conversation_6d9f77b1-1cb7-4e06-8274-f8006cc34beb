import { useLocalSearchParams } from 'expo-router';
import { StyleSheet, ScrollView, Pressable } from 'react-native';
import { ThemedView } from '@/components/ThemedView';
import { ThemedText } from '@/components/ThemedText';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Theme } from '@/constants/Theme';
import { Job } from '@/types';

// Mock data - replace with actual data fetching
const MOCK_JOB: Job = {
  id: '1',
  title: 'House Cleaning',
  description: 'Looking for someone to help clean my 2-bedroom apartment. Tasks include vacuuming, dusting, bathroom cleaning, and kitchen cleaning. All cleaning supplies will be provided. Expected duration: 3-4 hours.',
  reward: 100,
  location: 'Central District',
  status: 'open',
  postedBy: 'user1',
  postedDate: '2024-02-20',
  category: 'Cleaning',
};

export default function JobDetailsScreen() {
  const { id } = useLocalSearchParams();

  const handleTakeJob = () => {
    // Implement job taking logic
    console.log('Taking job:', id);
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <ThemedView style={styles.content}>
        <ThemedView style={styles.header}>
          <ThemedView style={styles.categoryTag}>
            <ThemedText style={styles.categoryTagText}>{MOCK_JOB.category}</ThemedText>
          </ThemedView>
          <ThemedText style={styles.date}>Posted on {MOCK_JOB.postedDate}</ThemedText>
        </ThemedView>

        <ThemedText style={styles.title}>{MOCK_JOB.title}</ThemedText>

        <ThemedView style={styles.rewardCard}>
          <ThemedView style={styles.rewardInfo}>
            <ThemedText style={styles.rewardLabel}>Reward</ThemedText>
            <ThemedText style={styles.rewardAmount}>${MOCK_JOB.reward}</ThemedText>
          </ThemedView>
          <Pressable style={styles.takeJobButton} onPress={handleTakeJob}>
            <ThemedText style={styles.buttonText}>Take Job</ThemedText>
          </Pressable>
        </ThemedView>

        <ThemedView style={styles.section}>
          <ThemedText style={styles.sectionTitle}>Location</ThemedText>
          <ThemedView style={styles.locationContainer}>
            <IconSymbol name="location.fill" size={20} color={Theme.colors.primary} />
            <ThemedText style={styles.location}>{MOCK_JOB.location}</ThemedText>
          </ThemedView>
        </ThemedView>

        <ThemedView style={styles.section}>
          <ThemedText style={styles.sectionTitle}>Description</ThemedText>
          <ThemedText style={styles.description}>{MOCK_JOB.description}</ThemedText>
        </ThemedView>

        <ThemedView style={styles.section}>
          <ThemedText style={styles.sectionTitle}>Posted By</ThemedText>
          <ThemedView style={styles.userCard}>
            <ThemedView style={styles.avatarContainer}>
              <IconSymbol name="person.fill" size={24} color={Theme.colors.primary} />
            </ThemedView>
            <ThemedView>
              <ThemedText style={styles.userName}>John Doe</ThemedText>
              <ThemedView style={styles.ratingContainer}>
                <IconSymbol name="star.fill" size={16} color={Theme.colors.warning} />
                <ThemedText style={styles.rating}>4.5/5</ThemedText>
              </ThemedView>
            </ThemedView>
          </ThemedView>
        </ThemedView>
      </ThemedView>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Theme.colors.background,
  },
  content: {
    padding: Theme.spacing.lg,
    gap: Theme.spacing.lg,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  categoryTag: {
    backgroundColor: Theme.colors.background,
    paddingHorizontal: Theme.spacing.sm,
    paddingVertical: Theme.spacing.xs,
    borderRadius: Theme.borderRadius.sm,
  },
  categoryTagText: {
    color: Theme.colors.primary,
    fontSize: 14,
    fontWeight: '500',
  },
  date: {
    fontSize: 14,
    color: Theme.colors.subtext,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Theme.colors.text,
  },
  rewardCard: {
    backgroundColor: Theme.colors.card,
    borderRadius: Theme.borderRadius.lg,
    padding: Theme.spacing.lg,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    ...Theme.shadows.md,
  },
  rewardInfo: {
    gap: Theme.spacing.xs,
  },
  rewardLabel: {
    fontSize: 14,
    color: Theme.colors.subtext,
  },
  rewardAmount: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Theme.colors.text,
  },
  takeJobButton: {
    backgroundColor: Theme.colors.primary,
    paddingHorizontal: Theme.spacing.lg,
    paddingVertical: Theme.spacing.md,
    borderRadius: Theme.borderRadius.lg,
  },
  buttonText: {
    color: Theme.colors.card,
    fontSize: 16,
    fontWeight: 'bold',
  },
  section: {
    gap: Theme.spacing.sm,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Theme.colors.text,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Theme.spacing.sm,
  },
  location: {
    fontSize: 16,
    color: Theme.colors.text,
  },
  description: {
    fontSize: 16,
    color: Theme.colors.text,
    lineHeight: 24,
  },
  userCard: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Theme.spacing.md,
    backgroundColor: Theme.colors.card,
    padding: Theme.spacing.md,
    borderRadius: Theme.borderRadius.lg,
    ...Theme.shadows.sm,
  },
  avatarContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: Theme.colors.background,
    alignItems: 'center',
    justifyContent: 'center',
  },
  userName: {
    fontSize: 16,
    fontWeight: '500',
    color: Theme.colors.text,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Theme.spacing.xs,
  },
  rating: {
    fontSize: 14,
    color: Theme.colors.subtext,
  },
});