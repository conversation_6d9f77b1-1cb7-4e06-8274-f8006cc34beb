import React, { ReactNode } from 'react';
import { View, StyleSheet, ActivityIndicator } from 'react-native';
import { router } from 'expo-router';
import { ThemedView } from '@/components/ThemedView';
import { ThemedText } from '@/components/ThemedText';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Theme } from '@/constants/Theme';
import { useAuthContext } from '@/contexts/AuthContext';
import { Pressable } from 'react-native';

interface AuthGuardProps {
  children: ReactNode;
  fallback?: ReactNode;
}

export function AuthGuard({ children, fallback }: AuthGuardProps) {
  const { isAuthenticated, loading } = useAuthContext();

  if (loading) {
    return (
      <ThemedView style={styles.container}>
        <ActivityIndicator size="large" color={Theme.colors.primary} />
        <ThemedText style={styles.loadingText}>Loading...</ThemedText>
      </ThemedView>
    );
  }

  if (!isAuthenticated) {
    return fallback || <DefaultAuthPrompt />;
  }

  return <>{children}</>;
}

function DefaultAuthPrompt() {
  const navigateToLogin = () => {
    router.push('/auth/login');
  };

  const navigateToRegister = () => {
    router.push('/auth/register');
  };

  return (
    <ThemedView style={styles.container}>
      <ThemedView style={styles.content}>
        <IconSymbol name="lock.fill" size={80} color={Theme.colors.primary} />
        <ThemedText style={styles.title}>Authentication Required</ThemedText>
        <ThemedText style={styles.subtitle}>
          Please sign in to access this feature
        </ThemedText>
        
        <ThemedView style={styles.buttonContainer}>
          <Pressable style={styles.primaryButton} onPress={navigateToLogin}>
            <IconSymbol name="arrow.right.circle.fill" size={20} color={Theme.colors.card} />
            <ThemedText style={styles.primaryButtonText}>Sign In</ThemedText>
          </Pressable>
          
          <Pressable style={styles.secondaryButton} onPress={navigateToRegister}>
            <IconSymbol name="person.badge.plus.fill" size={20} color={Theme.colors.primary} />
            <ThemedText style={styles.secondaryButtonText}>Create Account</ThemedText>
          </Pressable>
        </ThemedView>
      </ThemedView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Theme.colors.background,
    padding: Theme.spacing.xl,
  },
  content: {
    alignItems: 'center',
    gap: Theme.spacing.lg,
    maxWidth: 300,
  },
  loadingText: {
    marginTop: Theme.spacing.md,
    fontSize: 16,
    color: Theme.colors.subtext,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Theme.colors.text,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: Theme.colors.subtext,
    textAlign: 'center',
    lineHeight: 24,
  },
  buttonContainer: {
    gap: Theme.spacing.md,
    width: '100%',
    marginTop: Theme.spacing.lg,
  },
  primaryButton: {
    backgroundColor: Theme.colors.primary,
    padding: Theme.spacing.md,
    borderRadius: Theme.borderRadius.lg,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: Theme.spacing.sm,
  },
  primaryButtonText: {
    color: Theme.colors.card,
    fontSize: 16,
    fontWeight: 'bold',
  },
  secondaryButton: {
    backgroundColor: Theme.colors.card,
    padding: Theme.spacing.md,
    borderRadius: Theme.borderRadius.lg,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: Theme.spacing.sm,
    borderWidth: 1,
    borderColor: Theme.colors.border,
  },
  secondaryButtonText: {
    color: Theme.colors.primary,
    fontSize: 16,
    fontWeight: '500',
  },
});
