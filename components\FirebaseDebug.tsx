import React, { useState } from 'react';
import { View, Text, Pressable, StyleSheet, ScrollView } from 'react-native';
import { testFirebaseConnection, createSampleJob, createSampleUser } from '@/utils/debugFirebase';
import { Theme } from '@/constants/Theme';

export function FirebaseDebug() {
  const [logs, setLogs] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const addLog = (message: string) => {
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testConnection = async () => {
    setIsLoading(true);
    setLogs([]);
    
    try {
      addLog('🔍 Testing Firebase connection...');
      const result = await testFirebaseConnection();
      
      if (result) {
        addLog('✅ Firebase connection successful!');
      } else {
        addLog('❌ Firebase connection failed!');
      }
    } catch (error) {
      addLog(`❌ Error: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const createSampleData = async () => {
    setIsLoading(true);
    
    try {
      addLog('📝 Creating sample user...');
      await createSampleUser();
      addLog('✅ Sample user created!');
      
      addLog('📝 Creating sample job...');
      await createSampleJob();
      addLog('✅ Sample job created!');
      
      addLog('🎉 Sample data creation complete!');
    } catch (error) {
      addLog(`❌ Error creating sample data: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const clearLogs = () => {
    setLogs([]);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>🔥 Firebase Debug Panel</Text>
      
      <View style={styles.buttonContainer}>
        <Pressable 
          style={[styles.button, isLoading && styles.buttonDisabled]} 
          onPress={testConnection}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>Test Connection</Text>
        </Pressable>
        
        <Pressable 
          style={[styles.button, isLoading && styles.buttonDisabled]} 
          onPress={createSampleData}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>Create Sample Data</Text>
        </Pressable>
        
        <Pressable 
          style={[styles.button, styles.clearButton]} 
          onPress={clearLogs}
        >
          <Text style={styles.buttonText}>Clear Logs</Text>
        </Pressable>
      </View>
      
      <ScrollView style={styles.logsContainer}>
        <Text style={styles.logsTitle}>📋 Debug Logs:</Text>
        {logs.length === 0 ? (
          <Text style={styles.noLogs}>No logs yet. Press "Test Connection" to start.</Text>
        ) : (
          logs.map((log, index) => (
            <Text key={index} style={styles.logItem}>
              {log}
            </Text>
          ))
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: Theme.colors.background,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: Theme.colors.text,
  },
  buttonContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 10,
    marginBottom: 20,
  },
  button: {
    backgroundColor: Theme.colors.primary,
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderRadius: 8,
    flex: 1,
    minWidth: 100,
  },
  buttonDisabled: {
    backgroundColor: Theme.colors.subtext,
  },
  clearButton: {
    backgroundColor: Theme.colors.error,
  },
  buttonText: {
    color: 'white',
    textAlign: 'center',
    fontWeight: '500',
  },
  logsContainer: {
    flex: 1,
    backgroundColor: Theme.colors.card,
    borderRadius: 8,
    padding: 15,
  },
  logsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    color: Theme.colors.text,
  },
  noLogs: {
    color: Theme.colors.subtext,
    fontStyle: 'italic',
  },
  logItem: {
    fontSize: 12,
    marginBottom: 5,
    color: Theme.colors.text,
    fontFamily: 'monospace',
  },
});
