import React, { useState } from 'react';
import { StyleSheet, Pressable, ActivityIndicator, Alert } from 'react-native';
import { router } from 'expo-router';
import { ThemedView } from '@/components/ThemedView';
import { ThemedText } from '@/components/ThemedText';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Theme } from '@/constants/Theme';
import { useAuthContext } from '@/contexts/AuthContext';

interface LogoutConfirmationProps {
  onCancel: () => void;
}

export function LogoutConfirmation({ onCancel }: LogoutConfirmationProps) {
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const { logout, user } = useAuthContext();

  const handleLogout = async () => {
    setIsLoggingOut(true);
    try {
      console.log('🔐 User confirmed logout');
      await logout();
      console.log('✅ Logout successful');
      
      // Show success message and redirect
      Alert.alert(
        'Logged Out Successfully',
        'You have been logged out. Redirecting to login screen...',
        [
          {
            text: 'OK',
            onPress: () => router.replace('/auth/login')
          }
        ]
      );
    } catch (error) {
      console.error('❌ Logout error:', error);
      Alert.alert('Error', 'Failed to logout. Please try again.');
      setIsLoggingOut(false);
    }
  };

  return (
    <ThemedView style={styles.overlay}>
      <ThemedView style={styles.container}>
        <ThemedView style={styles.content}>
          <IconSymbol name="xmark.circle.fill" size={60} color={Theme.colors.error} />
          
          <ThemedText style={styles.title}>Logout Confirmation</ThemedText>
          
          <ThemedText style={styles.message}>
            Hi {user?.displayName || 'there'}! Are you sure you want to logout?
          </ThemedText>
          
          <ThemedText style={styles.submessage}>
            You will be redirected to the login screen and will need to sign in again to access your account.
          </ThemedText>

          <ThemedView style={styles.buttonContainer}>
            <Pressable 
              style={[styles.button, styles.cancelButton]} 
              onPress={onCancel}
              disabled={isLoggingOut}
            >
              <ThemedText style={styles.cancelButtonText}>Cancel</ThemedText>
            </Pressable>
            
            <Pressable 
              style={[styles.button, styles.logoutButton, isLoggingOut && styles.buttonDisabled]}
              onPress={handleLogout}
              disabled={isLoggingOut}
            >
              {isLoggingOut ? (
                <>
                  <ActivityIndicator size="small" color={Theme.colors.card} />
                  <ThemedText style={styles.logoutButtonText}>Logging out...</ThemedText>
                </>
              ) : (
                <>
                  <IconSymbol name="xmark.circle.fill" size={16} color={Theme.colors.card} />
                  <ThemedText style={styles.logoutButtonText}>Logout</ThemedText>
                </>
              )}
            </Pressable>
          </ThemedView>
        </ThemedView>
      </ThemedView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  container: {
    backgroundColor: Theme.colors.background,
    borderRadius: Theme.borderRadius.xl,
    margin: Theme.spacing.xl,
    maxWidth: 350,
    width: '90%',
    ...Theme.shadows.lg,
  },
  content: {
    padding: Theme.spacing.xl,
    alignItems: 'center',
    gap: Theme.spacing.lg,
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    color: Theme.colors.text,
    textAlign: 'center',
  },
  message: {
    fontSize: 16,
    color: Theme.colors.text,
    textAlign: 'center',
    lineHeight: 24,
  },
  submessage: {
    fontSize: 14,
    color: Theme.colors.subtext,
    textAlign: 'center',
    lineHeight: 20,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: Theme.spacing.md,
    width: '100%',
    marginTop: Theme.spacing.md,
  },
  button: {
    flex: 1,
    paddingVertical: Theme.spacing.md,
    paddingHorizontal: Theme.spacing.lg,
    borderRadius: Theme.borderRadius.lg,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: Theme.spacing.xs,
  },
  cancelButton: {
    backgroundColor: Theme.colors.card,
    borderWidth: 1,
    borderColor: Theme.colors.border,
  },
  cancelButtonText: {
    color: Theme.colors.text,
    fontSize: 16,
    fontWeight: '500',
  },
  logoutButton: {
    backgroundColor: Theme.colors.error,
  },
  logoutButtonText: {
    color: Theme.colors.card,
    fontSize: 16,
    fontWeight: 'bold',
  },
  buttonDisabled: {
    opacity: 0.7,
  },
});
