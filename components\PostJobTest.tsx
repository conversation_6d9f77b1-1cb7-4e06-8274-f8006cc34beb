import React from 'react';
import { View, Text, Pressable, StyleSheet, Alert } from 'react-native';
import { usePostJob } from '@/hooks/usePostJob';
import { Theme } from '@/constants/Theme';

export function PostJobTest() {
  const { postJob, isLoading } = usePostJob();

  const testPostJob = async () => {
    try {
      const testJobData = {
        title: 'Test Job - Quick Cleaning',
        description: 'This is a test job created to verify the posting functionality works correctly.',
        reward: 75,
        location: 'Test Location',
        category: 'Cleaning',
      };

      const jobId = await postJob(testJobData);
      
      if (jobId) {
        Alert.alert('Success!', `Test job created with ID: ${jobId}`);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to create test job');
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>🧪 Post Job Test</Text>
      <Text style={styles.description}>
        Click the button below to create a test job and verify the posting functionality.
      </Text>
      
      <Pressable 
        style={[styles.button, isLoading && styles.buttonDisabled]} 
        onPress={testPostJob}
        disabled={isLoading}
      >
        <Text style={styles.buttonText}>
          {isLoading ? 'Creating Test Job...' : 'Create Test Job'}
        </Text>
      </Pressable>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: Theme.colors.background,
    borderRadius: 8,
    margin: 10,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,
    color: Theme.colors.text,
  },
  description: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 20,
    color: Theme.colors.subtext,
  },
  button: {
    backgroundColor: Theme.colors.primary,
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonDisabled: {
    backgroundColor: Theme.colors.subtext,
    opacity: 0.7,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
});
