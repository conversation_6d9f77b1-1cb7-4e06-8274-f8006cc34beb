import React, { useEffect } from 'react';
import { StyleSheet, Pressable } from 'react-native';
import { router } from 'expo-router';
import { ThemedView } from '@/components/ThemedView';
import { ThemedText } from '@/components/ThemedText';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Theme } from '@/constants/Theme';
import { useAuthContext } from '@/contexts/AuthContext';

interface WelcomeScreenProps {
  isNewUser?: boolean;
  onContinue?: () => void;
}

export function WelcomeScreen({ isNewUser = false, onContinue }: WelcomeScreenProps) {
  const { user } = useAuthContext();

  const handleContinue = () => {
    if (onContinue) {
      onContinue();
    } else {
      router.replace('/(tabs)');
    }
  };

  // Auto-redirect after 3 seconds if no interaction
  useEffect(() => {
    const timer = setTimeout(() => {
      handleContinue();
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <ThemedView style={styles.container}>
      <ThemedView style={styles.content}>
        <IconSymbol 
          name={isNewUser ? "party.popper.fill" : "hand.wave.fill"} 
          size={100} 
          color={Theme.colors.primary} 
        />
        
        <ThemedText style={styles.title}>
          {isNewUser ? 'Welcome to HKServices!' : 'Welcome Back!'}
        </ThemedText>
        
        <ThemedText style={styles.subtitle}>
          {isNewUser 
            ? `Hi ${user?.displayName || 'there'}! Your account has been created successfully.`
            : `Hi ${user?.displayName || 'there'}! You have successfully signed in.`
          }
        </ThemedText>

        <ThemedText style={styles.description}>
          {isNewUser 
            ? 'You can now post jobs, browse available tasks, and connect with the community.'
            : 'Ready to explore jobs and manage your tasks?'
          }
        </ThemedText>

        <Pressable style={styles.continueButton} onPress={handleContinue}>
          <IconSymbol name="arrow.right.circle.fill" size={20} color={Theme.colors.card} />
          <ThemedText style={styles.continueButtonText}>
            {isNewUser ? 'Get Started' : 'Continue'}
          </ThemedText>
        </Pressable>

        <ThemedText style={styles.autoRedirectText}>
          Automatically redirecting in 3 seconds...
        </ThemedText>
      </ThemedView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Theme.colors.background,
    padding: Theme.spacing.xl,
  },
  content: {
    alignItems: 'center',
    gap: Theme.spacing.lg,
    maxWidth: 350,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Theme.colors.text,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 18,
    color: Theme.colors.text,
    textAlign: 'center',
    lineHeight: 26,
  },
  description: {
    fontSize: 16,
    color: Theme.colors.subtext,
    textAlign: 'center',
    lineHeight: 24,
  },
  continueButton: {
    backgroundColor: Theme.colors.primary,
    paddingHorizontal: Theme.spacing.xl,
    paddingVertical: Theme.spacing.md,
    borderRadius: Theme.borderRadius.lg,
    flexDirection: 'row',
    alignItems: 'center',
    gap: Theme.spacing.sm,
    marginTop: Theme.spacing.lg,
  },
  continueButtonText: {
    color: Theme.colors.card,
    fontSize: 18,
    fontWeight: 'bold',
  },
  autoRedirectText: {
    fontSize: 12,
    color: Theme.colors.subtext,
    textAlign: 'center',
    fontStyle: 'italic',
  },
});
