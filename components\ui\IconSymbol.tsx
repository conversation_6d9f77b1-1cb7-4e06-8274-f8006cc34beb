// This file is a fallback for using MaterialIcons on Android and web.

import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { SymbolWeight } from 'expo-symbols';
import React from 'react';
import { OpaqueColorValue, StyleProp, TextStyle } from 'react-native';

// Add your SFSymbol to MaterialIcons mappings here.
const MAPPING = {
  'briefcase.fill': 'work',             // Jobs/work icon
  'plus.circle.fill': 'add-circle',     // Original plus icon
  'person.circle.fill': 'account-circle', // Original person circle
  'location.fill': 'location-on',
  'star.fill': 'star',
  'paperplane.fill': 'send',
  'person.fill': 'person',
  'chevron.right': 'chevron-right',
  'chevron.left.forwardslash.chevron.right': 'code',
  'search.fill': 'search',
  'xmark.circle.fill': 'cancel',
  // Better tab icons
  'square.and.pencil': 'edit',          // Better post/create icon
  'doc.text.fill': 'description',       // Alternative post icon
  'plus.app.fill': 'post-add',          // Another post option
  'person.crop.circle': 'account-circle', // Clean profile icon
  'person.badge.plus': 'person-add',    // Profile with badge
  'user.circle': 'account-circle',      // Alternative profile
} as const;

export type IconSymbolName = keyof typeof MAPPING;

/**
 * An icon component that uses native SFSymbols on iOS, and MaterialIcons on Android and web. This ensures a consistent look across platforms, and optimal resource usage.
 *
 * Icon `name`s are based on SFSymbols and require manual mapping to MaterialIcons.
 */
export function IconSymbol({
  name,
  size = 24,
  color,
  style,
}: {
  name: IconSymbolName;
  size?: number;
  color: string | OpaqueColorValue;
  style?: StyleProp<TextStyle>;
  weight?: SymbolWeight;
}) {
  return <MaterialIcons color={color} size={size} name={MAPPING[name]} style={style} />;
}




