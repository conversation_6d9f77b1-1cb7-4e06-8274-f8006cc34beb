// This file is a fallback for using MaterialIcons on Android and web.

import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { SymbolWeight } from 'expo-symbols';
import React from 'react';
import { OpaqueColorValue, StyleProp, ViewStyle } from 'react-native';

// Add your SFSymbol to MaterialIcons mappings here.
const MAPPING = {
  'briefcase.fill': 'business_center',  // Changed from 'work'
  'plus.circle.fill': 'add_circle',     // Changed from 'add-circle'
  'person.circle.fill': 'account_circle', // Changed from 'account-circle'
  'location.fill': 'location_on',       // Changed from 'location-on'
  'star.fill': 'star',
  'paperplane.fill': 'send',
  'person.fill': 'person',
  'chevron.right': 'chevron_right',
  'chevron.left.forwardslash.chevron.right': 'code',
  'search.fill': 'search',
  'xmark.circle.fill': 'cancel',
} as const;

export type IconSymbolName = keyof typeof MAPPING;

/**
 * An icon component that uses native SFSymbols on iOS, and MaterialIcons on Android and web. This ensures a consistent look across platforms, and optimal resource usage.
 *
 * Icon `name`s are based on SFSymbols and require manual mapping to MaterialIcons.
 */
export function IconSymbol({
  name,
  size = 24,
  color,
  style,
}: {
  name: IconSymbolName;
  size?: number;
  color: string | OpaqueColorValue;
  style?: StyleProp<ViewStyle>;
  weight?: SymbolWeight;
}) {
  return <MaterialIcons color={color} size={size} name={MAPPING[name]} style={style} />;
}




