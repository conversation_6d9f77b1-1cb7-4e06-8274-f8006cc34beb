import { useState, useEffect, useCallback } from 'react';
import { AuthService, AuthUser, RegisterData, LoginData } from '@/services/authService';

interface AuthState {
  user: AuthUser | null;
  loading: boolean;
  error: string | null;
  isAuthenticated: boolean;
}

export const useAuth = () => {
  const [state, setState] = useState<AuthState>({
    user: null,
    loading: true,
    error: null,
    isAuthenticated: false,
  });

  // Register new user
  const register = useCallback(async (data: RegisterData): Promise<void> => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      const user = await AuthService.register(data);
      setState({
        user,
        loading: false,
        error: null,
        isAuthenticated: true,
      });
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Registration failed',
      }));
      throw error;
    }
  }, []);

  // Login user
  const login = useCallback(async (data: LoginData): Promise<void> => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      const user = await AuthService.login(data);
      setState({
        user,
        loading: false,
        error: null,
        isAuthenticated: true,
      });
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Login failed',
      }));
      throw error;
    }
  }, []);

  // Logout user
  const logout = useCallback(async (): Promise<void> => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      await AuthService.logout();
      setState({
        user: null,
        loading: false,
        error: null,
        isAuthenticated: false,
      });
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Logout failed',
      }));
      throw error;
    }
  }, []);

  // Send password reset email
  const sendPasswordReset = useCallback(async (email: string): Promise<void> => {
    try {
      setState(prev => ({ ...prev, error: null }));
      await AuthService.sendPasswordReset(email);
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to send password reset email',
      }));
      throw error;
    }
  }, []);

  // Update password
  const updatePassword = useCallback(async (currentPassword: string, newPassword: string): Promise<void> => {
    try {
      setState(prev => ({ ...prev, error: null }));
      await AuthService.updateUserPassword(currentPassword, newPassword);
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to update password',
      }));
      throw error;
    }
  }, []);

  // Update profile
  const updateProfile = useCallback(async (updates: { displayName?: string; photoURL?: string }): Promise<void> => {
    try {
      setState(prev => ({ ...prev, error: null }));
      await AuthService.updateUserProfile(updates);
      
      // Update local state
      setState(prev => ({
        ...prev,
        user: prev.user ? {
          ...prev.user,
          displayName: updates.displayName || prev.user.displayName,
          photoURL: updates.photoURL || prev.user.photoURL,
        } : null,
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to update profile',
      }));
      throw error;
    }
  }, []);

  // Clear error
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  // Listen to auth state changes
  useEffect(() => {
    console.log('🔐 Setting up auth state listener');
    
    const unsubscribe = AuthService.onAuthStateChanged((user) => {
      console.log('🔐 Auth state changed:', user ? `User: ${user.email}` : 'No user');
      
      setState({
        user,
        loading: false,
        error: null,
        isAuthenticated: !!user,
      });
    });

    return () => {
      console.log('🔐 Cleaning up auth state listener');
      unsubscribe();
    };
  }, []);

  return {
    ...state,
    register,
    login,
    logout,
    sendPasswordReset,
    updatePassword,
    updateProfile,
    clearError,
  };
};
