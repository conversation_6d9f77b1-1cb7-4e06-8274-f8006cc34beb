import { useEffect } from 'react';
import { router } from 'expo-router';
import { useAuthContext } from '@/contexts/AuthContext';

export const useAuthNavigation = () => {
  const { isAuthenticated, loading } = useAuthContext();

  useEffect(() => {
    // Don't navigate while auth state is loading
    if (loading) return;

    // If user becomes authenticated, redirect to main app
    if (isAuthenticated) {
      // Use replace to prevent going back to auth screens
      router.replace('/(tabs)');
    }
  }, [isAuthenticated, loading]);

  return { isAuthenticated, loading };
};

// Hook for protecting routes that require authentication
export const useRequireAuth = () => {
  const { isAuthenticated, loading } = useAuthContext();

  useEffect(() => {
    // Don't navigate while auth state is loading
    if (loading) return;

    // If user is not authenticated, redirect to login
    if (!isAuthenticated) {
      router.replace('/auth/login');
    }
  }, [isAuthenticated, loading]);

  return { isAuthenticated, loading };
};
