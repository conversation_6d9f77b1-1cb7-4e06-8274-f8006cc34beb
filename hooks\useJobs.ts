import { useState, useEffect, useCallback } from 'react';
import { JobService } from '@/services/jobService';
import { Job, JobsState } from '@/types';

export const useJobs = () => {
  const [state, setState] = useState<JobsState>({
    jobs: [],
    loading: true,
    error: null,
  });

  const fetchJobs = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      const jobs = await JobService.getAllJobs();
      setState({ jobs, loading: false, error: null });
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        loading: false, 
        error: error instanceof Error ? error.message : 'Failed to fetch jobs' 
      }));
    }
  }, []);

  const fetchJobsByCategory = useCallback(async (category: string) => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      const jobs = category === 'All' 
        ? await JobService.getAllJobs()
        : await JobService.getJobsByCategory(category);
      setState({ jobs, loading: false, error: null });
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        loading: false, 
        error: error instanceof Error ? error.message : 'Failed to fetch jobs' 
      }));
    }
  }, []);

  const searchJobs = useCallback(async (searchTerm: string) => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      const jobs = searchTerm.trim() === '' 
        ? await JobService.getAllJobs()
        : await JobService.searchJobs(searchTerm);
      setState({ jobs, loading: false, error: null });
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        loading: false, 
        error: error instanceof Error ? error.message : 'Failed to search jobs' 
      }));
    }
  }, []);

  const takeJob = useCallback(async (jobId: string, userId: string) => {
    try {
      await JobService.takeJob(jobId, userId);
      // Refresh jobs after taking a job
      await fetchJobs();
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : 'Failed to take job');
    }
  }, [fetchJobs]);

  const completeJob = useCallback(async (jobId: string) => {
    try {
      await JobService.completeJob(jobId);
      // Refresh jobs after completing a job
      await fetchJobs();
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : 'Failed to complete job');
    }
  }, [fetchJobs]);

  useEffect(() => {
    fetchJobs();
  }, [fetchJobs]);

  return {
    ...state,
    fetchJobs,
    fetchJobsByCategory,
    searchJobs,
    takeJob,
    completeJob,
    refetch: fetchJobs,
  };
};

export const useJob = (jobId: string) => {
  const [job, setJob] = useState<Job | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchJob = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const jobData = await JobService.getJobById(jobId);
      setJob(jobData);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to fetch job');
    } finally {
      setLoading(false);
    }
  }, [jobId]);

  useEffect(() => {
    if (jobId) {
      fetchJob();
    }
  }, [jobId, fetchJob]);

  return {
    job,
    loading,
    error,
    refetch: fetchJob,
  };
};

export const useJobsRealtime = () => {
  const [state, setState] = useState<JobsState>({
    jobs: [],
    loading: true,
    error: null,
  });

  useEffect(() => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    const unsubscribe = JobService.subscribeToJobs((jobs) => {
      setState({ jobs, loading: false, error: null });
    });

    return () => {
      unsubscribe();
    };
  }, []);

  return state;
};

export const useJobRealtime = (jobId: string) => {
  const [job, setJob] = useState<Job | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!jobId) return;

    setLoading(true);
    setError(null);
    
    const unsubscribe = JobService.subscribeToJob(jobId, (jobData) => {
      setJob(jobData);
      setLoading(false);
    });

    return () => {
      unsubscribe();
    };
  }, [jobId]);

  return {
    job,
    loading,
    error,
  };
};
