import { useState } from 'react';
import { Alert } from 'react-native';
import { JobService } from '@/services/jobService';
import { UserService } from '@/services/userService';
import { useAuthContext } from '@/contexts/AuthContext';

export interface JobFormData {
  title: string;
  description: string;
  reward: number;
  location: string;
  category: string;
}

export const usePostJob = () => {
  const [isLoading, setIsLoading] = useState(false);
  const { user: authUser, isAuthenticated } = useAuthContext();

  const postJob = async (jobData: JobFormData): Promise<string | null> => {
    if (!authUser || !isAuthenticated) {
      Alert.alert('Error', 'Please log in to post a job.');
      return null;
    }

    setIsLoading(true);
    
    try {
      console.log('📝 Creating new job...');
      
      // Create job data with additional fields
      const completeJobData = {
        ...jobData,
        status: 'open' as const,
        postedBy: authUser.uid,
        postedDate: new Date().toISOString().split('T')[0], // YYYY-MM-DD format
      };

      console.log('📊 Job data:', completeJobData);

      // Create the job in Firebase
      const jobId = await JobService.createJob(completeJobData);
      console.log('✅ Job created with ID:', jobId);

      // Add job to user's posted jobs
      await UserService.addPostedJob(authUser.uid, jobId);
      console.log('✅ Job added to user profile');

      return jobId;

    } catch (error) {
      console.error('❌ Error posting job:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    postJob,
    isLoading,
  };
};
