import { useState } from 'react';
import { Alert } from 'react-native';
import { JobService } from '@/services/jobService';
import { UserService } from '@/services/userService';
import { useUser } from '@/hooks/useUser';

export interface JobFormData {
  title: string;
  description: string;
  reward: number;
  location: string;
  category: string;
}

export const usePostJob = () => {
  const [isLoading, setIsLoading] = useState(false);
  const { user } = useUser();

  const postJob = async (jobData: JobFormData): Promise<string | null> => {
    if (!user) {
      Alert.alert('Error', 'User not found. Please try again.');
      return null;
    }

    setIsLoading(true);
    
    try {
      console.log('📝 Creating new job...');
      
      // Create job data with additional fields
      const completeJobData = {
        ...jobData,
        status: 'open' as const,
        postedBy: user.id,
        postedDate: new Date().toISOString().split('T')[0], // YYYY-MM-DD format
      };

      console.log('📊 Job data:', completeJobData);

      // Create the job in Firebase
      const jobId = await JobService.createJob(completeJobData);
      console.log('✅ Job created with ID:', jobId);

      // Add job to user's posted jobs
      await UserService.addPostedJob(user.id, jobId);
      console.log('✅ Job added to user profile');

      return jobId;

    } catch (error) {
      console.error('❌ Error posting job:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    postJob,
    isLoading,
  };
};
