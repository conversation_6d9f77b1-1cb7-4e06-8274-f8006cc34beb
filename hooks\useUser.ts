import { useState, useEffect, useCallback } from 'react';
import { UserService } from '@/services/userService';
import { JobService } from '@/services/jobService';
import { User, UserState, Job } from '@/types';

// For demo purposes, we'll use a mock user ID
// In a real app, this would come from authentication
const MOCK_USER_ID = 'demo-user-123';

export const useUser = (userId: string = MOCK_USER_ID) => {
  const [state, setState] = useState<UserState>({
    user: null,
    loading: true,
    error: null,
  });

  const fetchUser = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      let user = await UserService.getUserById(userId);
      
      // If user doesn't exist, create a demo user
      if (!user) {
        await UserService.createOrUpdateUser(userId, {
          name: '<PERSON>',
          email: '<EMAIL>',
          rating: 4.5,
        });
        user = await UserService.getUserById(userId);
      }
      
      setState({ user, loading: false, error: null });
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        loading: false, 
        error: error instanceof Error ? error.message : 'Failed to fetch user' 
      }));
    }
  }, [userId]);

  const updateProfile = useCallback(async (updates: Partial<User>) => {
    try {
      await UserService.updateUserProfile(userId, updates);
      await fetchUser(); // Refresh user data
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : 'Failed to update profile');
    }
  }, [userId, fetchUser]);

  useEffect(() => {
    fetchUser();
  }, [fetchUser]);

  return {
    ...state,
    fetchUser,
    updateProfile,
    refetch: fetchUser,
  };
};

export const useUserJobs = (userId: string = MOCK_USER_ID) => {
  const [postedJobs, setPostedJobs] = useState<Job[]>([]);
  const [takenJobs, setTakenJobs] = useState<Job[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchUserJobs = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const [posted, taken] = await Promise.all([
        JobService.getJobsByUser(userId),
        JobService.getJobsTakenByUser(userId),
      ]);
      
      setPostedJobs(posted);
      setTakenJobs(taken);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to fetch user jobs');
    } finally {
      setLoading(false);
    }
  }, [userId]);

  useEffect(() => {
    fetchUserJobs();
  }, [fetchUserJobs]);

  return {
    postedJobs,
    takenJobs,
    loading,
    error,
    refetch: fetchUserJobs,
  };
};

export const useUserStats = (userId: string = MOCK_USER_ID) => {
  const [stats, setStats] = useState({
    jobsPosted: 0,
    jobsTaken: 0,
    rating: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchStats = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const userStats = await UserService.getUserStats(userId);
      setStats(userStats);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to fetch user stats');
    } finally {
      setLoading(false);
    }
  }, [userId]);

  useEffect(() => {
    fetchStats();
  }, [fetchStats]);

  return {
    stats,
    loading,
    error,
    refetch: fetchStats,
  };
};
