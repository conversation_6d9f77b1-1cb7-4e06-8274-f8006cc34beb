import { useState, useEffect, useCallback } from 'react';
import { UserService } from '@/services/userService';
import { JobService } from '@/services/jobService';
import { User, UserState, Job } from '@/types';
import { useAuthContext } from '@/contexts/AuthContext';

export const useUser = () => {
  const { user: authUser, isAuthenticated } = useAuthContext();
  const [state, setState] = useState<UserState>({
    user: null,
    loading: true,
    error: null,
  });

  const fetchUser = useCallback(async () => {
    if (!authUser || !isAuthenticated) {
      setState({ user: null, loading: false, error: null });
      return;
    }

    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      let user = await UserService.getUserById(authUser.uid);

      // If user doesn't exist in Firestore, create from auth user
      if (!user) {
        await UserService.createOrUpdateUser(authUser.uid, {
          name: authUser.displayName || 'User',
          email: authUser.email || '',
          rating: 0,
        });
        user = await UserService.getUserById(authUser.uid);
      }

      setState({ user, loading: false, error: null });
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch user'
      }));
    }
  }, [authUser, isAuthenticated]);

  const updateProfile = useCallback(async (updates: Partial<User>) => {
    if (!authUser) {
      throw new Error('No authenticated user');
    }

    try {
      await UserService.updateUserProfile(authUser.uid, updates);
      await fetchUser(); // Refresh user data
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : 'Failed to update profile');
    }
  }, [authUser, fetchUser]);

  useEffect(() => {
    fetchUser();
  }, [fetchUser]);

  return {
    ...state,
    fetchUser,
    updateProfile,
    refetch: fetchUser,
  };
};

export const useUserJobs = () => {
  const { user: authUser, isAuthenticated } = useAuthContext();
  const [postedJobs, setPostedJobs] = useState<Job[]>([]);
  const [takenJobs, setTakenJobs] = useState<Job[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchUserJobs = useCallback(async () => {
    if (!authUser || !isAuthenticated) {
      setPostedJobs([]);
      setTakenJobs([]);
      setLoading(false);
      setError(null);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const [posted, taken] = await Promise.all([
        JobService.getJobsByUser(authUser.uid),
        JobService.getJobsTakenByUser(authUser.uid),
      ]);

      setPostedJobs(posted);
      setTakenJobs(taken);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to fetch user jobs');
    } finally {
      setLoading(false);
    }
  }, [authUser, isAuthenticated]);

  useEffect(() => {
    fetchUserJobs();
  }, [fetchUserJobs]);

  return {
    postedJobs,
    takenJobs,
    loading,
    error,
    refetch: fetchUserJobs,
  };
};

export const useUserStats = () => {
  const { user: authUser, isAuthenticated } = useAuthContext();
  const [stats, setStats] = useState({
    jobsPosted: 0,
    jobsTaken: 0,
    rating: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchStats = useCallback(async () => {
    if (!authUser || !isAuthenticated) {
      setStats({ jobsPosted: 0, jobsTaken: 0, rating: 0 });
      setLoading(false);
      setError(null);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const userStats = await UserService.getUserStats(authUser.uid);
      setStats(userStats);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to fetch user stats');
    } finally {
      setLoading(false);
    }
  }, [authUser, isAuthenticated]);

  useEffect(() => {
    fetchStats();
  }, [fetchStats]);

  return {
    stats,
    loading,
    error,
    refetch: fetchStats,
  };
};
