import { JobService } from '../services/jobService';
import { UserService } from '../services/userService';

const sampleJobs = [
  {
    title: 'House Cleaning',
    description: 'Need help cleaning 2-bedroom apartment. Tasks include vacuuming, dusting, bathroom cleaning, and kitchen cleaning. All cleaning supplies will be provided. Expected duration: 3-4 hours.',
    reward: 100,
    location: 'Central District',
    status: 'open' as const,
    postedBy: 'demo-user-123',
    postedDate: '2024-02-20',
    category: 'Cleaning',
  },
  {
    title: 'Moving Assistance',
    description: 'Help needed for moving furniture to new apartment. Heavy lifting required. Moving truck will be provided. Expected duration: 4-5 hours.',
    reward: 150,
    location: 'North Point',
    status: 'open' as const,
    postedBy: 'demo-user-123',
    postedDate: '2024-02-21',
    category: 'Moving',
  },
  {
    title: 'Grocery Delivery',
    description: 'Need someone to pick up groceries from supermarket and deliver to my apartment. Shopping list will be provided.',
    reward: 50,
    location: '<PERSON> Cha<PERSON>',
    status: 'open' as const,
    postedBy: 'demo-user-456',
    postedDate: '2024-02-22',
    category: 'Delivery',
  },
  {
    title: 'Furniture Assembly',
    description: 'Need help assembling IKEA furniture - 1 wardrobe and 2 bookshelves. Tools will be provided.',
    reward: 80,
    location: 'Tsim Sha Tsui',
    status: 'open' as const,
    postedBy: 'demo-user-789',
    postedDate: '2024-02-23',
    category: 'Maintenance',
  },
  {
    title: 'Dog Walking',
    description: 'Looking for someone to walk my friendly golden retriever for 1 hour in the park.',
    reward: 40,
    location: 'Mid-Levels',
    status: 'completed' as const,
    postedBy: 'demo-user-123',
    postedDate: '2024-02-18',
    category: 'Pet Care',
    takenBy: 'demo-user-456',
  },
];

const sampleUsers = [
  {
    id: 'demo-user-123',
    name: 'John Doe',
    email: '<EMAIL>',
    rating: 4.5,
  },
  {
    id: 'demo-user-456',
    name: 'Jane Smith',
    email: '<EMAIL>',
    rating: 4.8,
  },
  {
    id: 'demo-user-789',
    name: 'Mike Johnson',
    email: '<EMAIL>',
    rating: 4.2,
  },
];

export async function seedData() {
  try {
    console.log('Starting data seeding...');

    // Create users first
    for (const user of sampleUsers) {
      await UserService.createOrUpdateUser(user.id, user);
      console.log(`Created user: ${user.name}`);
    }

    // Create jobs
    for (const job of sampleJobs) {
      const jobId = await JobService.createJob(job);
      
      // Add job to user's posted jobs
      await UserService.addPostedJob(job.postedBy, jobId);
      
      // If job is taken, add to taker's taken jobs
      if (job.takenBy) {
        await UserService.addTakenJob(job.takenBy, jobId);
      }
      
      console.log(`Created job: ${job.title}`);
    }

    console.log('Data seeding completed successfully!');
  } catch (error) {
    console.error('Error seeding data:', error);
    throw error;
  }
}

// Run seeding if this file is executed directly
if (require.main === module) {
  seedData()
    .then(() => {
      console.log('Seeding finished');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Seeding failed:', error);
      process.exit(1);
    });
}
