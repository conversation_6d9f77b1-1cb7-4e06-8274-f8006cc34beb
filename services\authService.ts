import { 
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  updateProfile,
  User as FirebaseUser,
  sendPasswordResetEmail,
  updatePassword,
  EmailAuthProvider,
  reauthenticateWithCredential
} from 'firebase/auth';
import { auth } from '@/config/firebase';
import { UserService } from './userService';

export interface AuthUser {
  uid: string;
  email: string | null;
  displayName: string | null;
  photoURL: string | null;
  emailVerified: boolean;
}

export interface RegisterData {
  email: string;
  password: string;
  name: string;
}

export interface LoginData {
  email: string;
  password: string;
}

export class AuthService {
  // Register new user
  static async register(data: RegisterData): Promise<AuthUser> {
    try {
      console.log('🔐 Registering new user:', data.email);
      
      // Create user with email and password
      const userCredential = await createUserWithEmailAndPassword(
        auth, 
        data.email, 
        data.password
      );
      
      const firebaseUser = userCredential.user;
      console.log('✅ Firebase user created:', firebaseUser.uid);
      
      // Update display name
      await updateProfile(firebaseUser, {
        displayName: data.name
      });
      
      // Create user profile in Firestore
      await UserService.createOrUpdateUser(firebaseUser.uid, {
        name: data.name,
        email: data.email,
        rating: 0,
        postedJobs: [],
        takenJobs: [],
      });
      
      console.log('✅ User profile created in Firestore');
      
      return {
        uid: firebaseUser.uid,
        email: firebaseUser.email,
        displayName: firebaseUser.displayName,
        photoURL: firebaseUser.photoURL,
        emailVerified: firebaseUser.emailVerified,
      };
    } catch (error: any) {
      console.error('❌ Registration error:', error);
      throw new Error(this.getAuthErrorMessage(error.code));
    }
  }

  // Login user
  static async login(data: LoginData): Promise<AuthUser> {
    try {
      console.log('🔐 Logging in user:', data.email);
      
      const userCredential = await signInWithEmailAndPassword(
        auth,
        data.email,
        data.password
      );
      
      const firebaseUser = userCredential.user;
      console.log('✅ User logged in:', firebaseUser.uid);
      
      // Ensure user profile exists in Firestore
      const existingUser = await UserService.getUserById(firebaseUser.uid);
      if (!existingUser) {
        await UserService.createOrUpdateUser(firebaseUser.uid, {
          name: firebaseUser.displayName || 'User',
          email: firebaseUser.email || '',
          rating: 0,
          postedJobs: [],
          takenJobs: [],
        });
      }
      
      return {
        uid: firebaseUser.uid,
        email: firebaseUser.email,
        displayName: firebaseUser.displayName,
        photoURL: firebaseUser.photoURL,
        emailVerified: firebaseUser.emailVerified,
      };
    } catch (error: any) {
      console.error('❌ Login error:', error);
      throw new Error(this.getAuthErrorMessage(error.code));
    }
  }

  // Logout user
  static async logout(): Promise<void> {
    try {
      console.log('🔐 Logging out user');
      await signOut(auth);
      console.log('✅ User logged out');
    } catch (error) {
      console.error('❌ Logout error:', error);
      throw new Error('Failed to logout');
    }
  }

  // Get current user
  static getCurrentUser(): FirebaseUser | null {
    return auth.currentUser;
  }

  // Listen to auth state changes
  static onAuthStateChanged(callback: (user: AuthUser | null) => void): () => void {
    return onAuthStateChanged(auth, (firebaseUser) => {
      if (firebaseUser) {
        const authUser: AuthUser = {
          uid: firebaseUser.uid,
          email: firebaseUser.email,
          displayName: firebaseUser.displayName,
          photoURL: firebaseUser.photoURL,
          emailVerified: firebaseUser.emailVerified,
        };
        callback(authUser);
      } else {
        callback(null);
      }
    });
  }

  // Send password reset email
  static async sendPasswordReset(email: string): Promise<void> {
    try {
      await sendPasswordResetEmail(auth, email);
      console.log('✅ Password reset email sent');
    } catch (error: any) {
      console.error('❌ Password reset error:', error);
      throw new Error(this.getAuthErrorMessage(error.code));
    }
  }

  // Update user password
  static async updateUserPassword(currentPassword: string, newPassword: string): Promise<void> {
    try {
      const user = auth.currentUser;
      if (!user || !user.email) {
        throw new Error('No authenticated user found');
      }

      // Re-authenticate user
      const credential = EmailAuthProvider.credential(user.email, currentPassword);
      await reauthenticateWithCredential(user, credential);
      
      // Update password
      await updatePassword(user, newPassword);
      console.log('✅ Password updated');
    } catch (error: any) {
      console.error('❌ Password update error:', error);
      throw new Error(this.getAuthErrorMessage(error.code));
    }
  }

  // Update user profile
  static async updateUserProfile(updates: { displayName?: string; photoURL?: string }): Promise<void> {
    try {
      const user = auth.currentUser;
      if (!user) {
        throw new Error('No authenticated user found');
      }

      await updateProfile(user, updates);
      
      // Also update in Firestore
      if (updates.displayName) {
        await UserService.updateUserProfile(user.uid, {
          name: updates.displayName
        });
      }
      
      console.log('✅ Profile updated');
    } catch (error) {
      console.error('❌ Profile update error:', error);
      throw new Error('Failed to update profile');
    }
  }

  // Convert Firebase auth error codes to user-friendly messages
  private static getAuthErrorMessage(errorCode: string): string {
    switch (errorCode) {
      case 'auth/email-already-in-use':
        return 'This email address is already registered. Please use a different email or try logging in.';
      case 'auth/weak-password':
        return 'Password is too weak. Please choose a stronger password with at least 6 characters.';
      case 'auth/invalid-email':
        return 'Please enter a valid email address.';
      case 'auth/user-not-found':
        return 'No account found with this email address. Please check your email or register for a new account.';
      case 'auth/wrong-password':
        return 'Incorrect password. Please try again or reset your password.';
      case 'auth/too-many-requests':
        return 'Too many failed attempts. Please try again later.';
      case 'auth/network-request-failed':
        return 'Network error. Please check your internet connection and try again.';
      case 'auth/requires-recent-login':
        return 'This operation requires recent authentication. Please log out and log back in.';
      default:
        return 'An error occurred during authentication. Please try again.';
    }
  }
}
