import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  onSnapshot,
  serverTimestamp,
  Timestamp 
} from 'firebase/firestore';
import { db } from '@/config/firebase';
import { Job } from '@/types';

const JOBS_COLLECTION = 'jobs';

export class JobService {
  // Get all jobs
  static async getAllJobs(): Promise<Job[]> {
    try {
      const jobsRef = collection(db, JOBS_COLLECTION);
      const q = query(jobsRef, orderBy('createdAt', 'desc'));
      const querySnapshot = await getDocs(q);
      
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate?.() || new Date(),
        updatedAt: doc.data().updatedAt?.toDate?.() || new Date(),
      })) as Job[];
    } catch (error) {
      console.error('Error fetching jobs:', error);
      throw new Error('Failed to fetch jobs');
    }
  }

  // Get jobs by category
  static async getJobsByCategory(category: string): Promise<Job[]> {
    try {
      const jobsRef = collection(db, JOBS_COLLECTION);
      const q = query(
        jobsRef, 
        where('category', '==', category),
        orderBy('createdAt', 'desc')
      );
      const querySnapshot = await getDocs(q);
      
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate?.() || new Date(),
        updatedAt: doc.data().updatedAt?.toDate?.() || new Date(),
      })) as Job[];
    } catch (error) {
      console.error('Error fetching jobs by category:', error);
      throw new Error('Failed to fetch jobs by category');
    }
  }

  // Get job by ID
  static async getJobById(jobId: string): Promise<Job | null> {
    try {
      const jobRef = doc(db, JOBS_COLLECTION, jobId);
      const jobSnap = await getDoc(jobRef);
      
      if (jobSnap.exists()) {
        return {
          id: jobSnap.id,
          ...jobSnap.data(),
          createdAt: jobSnap.data().createdAt?.toDate?.() || new Date(),
          updatedAt: jobSnap.data().updatedAt?.toDate?.() || new Date(),
        } as Job;
      }
      
      return null;
    } catch (error) {
      console.error('Error fetching job:', error);
      throw new Error('Failed to fetch job');
    }
  }

  // Create a new job
  static async createJob(jobData: Omit<Job, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const jobsRef = collection(db, JOBS_COLLECTION);
      const docRef = await addDoc(jobsRef, {
        ...jobData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      });
      
      return docRef.id;
    } catch (error) {
      console.error('Error creating job:', error);
      throw new Error('Failed to create job');
    }
  }

  // Update a job
  static async updateJob(jobId: string, updates: Partial<Job>): Promise<void> {
    try {
      const jobRef = doc(db, JOBS_COLLECTION, jobId);
      await updateDoc(jobRef, {
        ...updates,
        updatedAt: serverTimestamp(),
      });
    } catch (error) {
      console.error('Error updating job:', error);
      throw new Error('Failed to update job');
    }
  }

  // Delete a job
  static async deleteJob(jobId: string): Promise<void> {
    try {
      const jobRef = doc(db, JOBS_COLLECTION, jobId);
      await deleteDoc(jobRef);
    } catch (error) {
      console.error('Error deleting job:', error);
      throw new Error('Failed to delete job');
    }
  }

  // Get jobs posted by a user
  static async getJobsByUser(userId: string): Promise<Job[]> {
    try {
      const jobsRef = collection(db, JOBS_COLLECTION);
      const q = query(
        jobsRef, 
        where('postedBy', '==', userId),
        orderBy('createdAt', 'desc')
      );
      const querySnapshot = await getDocs(q);
      
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate?.() || new Date(),
        updatedAt: doc.data().updatedAt?.toDate?.() || new Date(),
      })) as Job[];
    } catch (error) {
      console.error('Error fetching user jobs:', error);
      throw new Error('Failed to fetch user jobs');
    }
  }

  // Get jobs taken by a user
  static async getJobsTakenByUser(userId: string): Promise<Job[]> {
    try {
      const jobsRef = collection(db, JOBS_COLLECTION);
      const q = query(
        jobsRef, 
        where('takenBy', '==', userId),
        orderBy('createdAt', 'desc')
      );
      const querySnapshot = await getDocs(q);
      
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate?.() || new Date(),
        updatedAt: doc.data().updatedAt?.toDate?.() || new Date(),
      })) as Job[];
    } catch (error) {
      console.error('Error fetching taken jobs:', error);
      throw new Error('Failed to fetch taken jobs');
    }
  }

  // Real-time listener for all jobs
  static subscribeToJobs(callback: (jobs: Job[]) => void): () => void {
    const jobsRef = collection(db, JOBS_COLLECTION);
    const q = query(jobsRef, orderBy('createdAt', 'desc'));
    
    return onSnapshot(q, (querySnapshot) => {
      const jobs = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate?.() || new Date(),
        updatedAt: doc.data().updatedAt?.toDate?.() || new Date(),
      })) as Job[];
      
      callback(jobs);
    }, (error) => {
      console.error('Error in jobs subscription:', error);
    });
  }

  // Real-time listener for a specific job
  static subscribeToJob(jobId: string, callback: (job: Job | null) => void): () => void {
    const jobRef = doc(db, JOBS_COLLECTION, jobId);
    
    return onSnapshot(jobRef, (doc) => {
      if (doc.exists()) {
        const job = {
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt?.toDate?.() || new Date(),
          updatedAt: doc.data().updatedAt?.toDate?.() || new Date(),
        } as Job;
        callback(job);
      } else {
        callback(null);
      }
    }, (error) => {
      console.error('Error in job subscription:', error);
    });
  }

  // Take a job
  static async takeJob(jobId: string, userId: string): Promise<void> {
    try {
      await this.updateJob(jobId, {
        takenBy: userId,
        status: 'in-progress'
      });
    } catch (error) {
      console.error('Error taking job:', error);
      throw new Error('Failed to take job');
    }
  }

  // Complete a job
  static async completeJob(jobId: string): Promise<void> {
    try {
      await this.updateJob(jobId, {
        status: 'completed'
      });
    } catch (error) {
      console.error('Error completing job:', error);
      throw new Error('Failed to complete job');
    }
  }

  // Search jobs
  static async searchJobs(searchTerm: string): Promise<Job[]> {
    try {
      // Note: Firestore doesn't support full-text search natively
      // This is a basic implementation that fetches all jobs and filters client-side
      // For production, consider using Algolia or similar service for better search
      const allJobs = await this.getAllJobs();
      
      const searchTermLower = searchTerm.toLowerCase();
      return allJobs.filter(job => 
        job.title.toLowerCase().includes(searchTermLower) ||
        job.description.toLowerCase().includes(searchTermLower) ||
        job.location.toLowerCase().includes(searchTermLower) ||
        job.category.toLowerCase().includes(searchTermLower)
      );
    } catch (error) {
      console.error('Error searching jobs:', error);
      throw new Error('Failed to search jobs');
    }
  }
}
