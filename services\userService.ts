import { 
  collection, 
  doc, 
  getDoc, 
  setDoc, 
  updateDoc, 
  serverTimestamp,
  arrayUnion,
  arrayRemove 
} from 'firebase/firestore';
import { db } from '@/config/firebase';
import { User } from '@/types';

const USERS_COLLECTION = 'users';

export class UserService {
  // Get user by ID
  static async getUserById(userId: string): Promise<User | null> {
    try {
      const userRef = doc(db, USERS_COLLECTION, userId);
      const userSnap = await getDoc(userRef);
      
      if (userSnap.exists()) {
        return {
          id: userSnap.id,
          ...userSnap.data(),
          createdAt: userSnap.data().createdAt?.toDate?.() || new Date(),
          updatedAt: userSnap.data().updatedAt?.toDate?.() || new Date(),
        } as User;
      }
      
      return null;
    } catch (error) {
      console.error('Error fetching user:', error);
      throw new Error('Failed to fetch user');
    }
  }

  // Create or update user
  static async createOrUpdateUser(userId: string, userData: Partial<User>): Promise<void> {
    try {
      const userRef = doc(db, USERS_COLLECTION, userId);
      const userSnap = await getDoc(userRef);
      
      if (userSnap.exists()) {
        // Update existing user
        await updateDoc(userRef, {
          ...userData,
          updatedAt: serverTimestamp(),
        });
      } else {
        // Create new user
        await setDoc(userRef, {
          id: userId,
          postedJobs: [],
          takenJobs: [],
          rating: 0,
          ...userData,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
        });
      }
    } catch (error) {
      console.error('Error creating/updating user:', error);
      throw new Error('Failed to create/update user');
    }
  }

  // Update user profile
  static async updateUserProfile(userId: string, updates: Partial<User>): Promise<void> {
    try {
      const userRef = doc(db, USERS_COLLECTION, userId);
      await updateDoc(userRef, {
        ...updates,
        updatedAt: serverTimestamp(),
      });
    } catch (error) {
      console.error('Error updating user profile:', error);
      throw new Error('Failed to update user profile');
    }
  }

  // Add job to user's posted jobs
  static async addPostedJob(userId: string, jobId: string): Promise<void> {
    try {
      const userRef = doc(db, USERS_COLLECTION, userId);
      await updateDoc(userRef, {
        postedJobs: arrayUnion(jobId),
        updatedAt: serverTimestamp(),
      });
    } catch (error) {
      console.error('Error adding posted job:', error);
      throw new Error('Failed to add posted job');
    }
  }

  // Remove job from user's posted jobs
  static async removePostedJob(userId: string, jobId: string): Promise<void> {
    try {
      const userRef = doc(db, USERS_COLLECTION, userId);
      await updateDoc(userRef, {
        postedJobs: arrayRemove(jobId),
        updatedAt: serverTimestamp(),
      });
    } catch (error) {
      console.error('Error removing posted job:', error);
      throw new Error('Failed to remove posted job');
    }
  }

  // Add job to user's taken jobs
  static async addTakenJob(userId: string, jobId: string): Promise<void> {
    try {
      const userRef = doc(db, USERS_COLLECTION, userId);
      await updateDoc(userRef, {
        takenJobs: arrayUnion(jobId),
        updatedAt: serverTimestamp(),
      });
    } catch (error) {
      console.error('Error adding taken job:', error);
      throw new Error('Failed to add taken job');
    }
  }

  // Remove job from user's taken jobs
  static async removeTakenJob(userId: string, jobId: string): Promise<void> {
    try {
      const userRef = doc(db, USERS_COLLECTION, userId);
      await updateDoc(userRef, {
        takenJobs: arrayRemove(jobId),
        updatedAt: serverTimestamp(),
      });
    } catch (error) {
      console.error('Error removing taken job:', error);
      throw new Error('Failed to remove taken job');
    }
  }

  // Update user rating
  static async updateUserRating(userId: string, newRating: number): Promise<void> {
    try {
      const userRef = doc(db, USERS_COLLECTION, userId);
      await updateDoc(userRef, {
        rating: newRating,
        updatedAt: serverTimestamp(),
      });
    } catch (error) {
      console.error('Error updating user rating:', error);
      throw new Error('Failed to update user rating');
    }
  }

  // Get user statistics
  static async getUserStats(userId: string): Promise<{
    jobsPosted: number;
    jobsTaken: number;
    rating: number;
  }> {
    try {
      const user = await this.getUserById(userId);
      if (!user) {
        return { jobsPosted: 0, jobsTaken: 0, rating: 0 };
      }

      return {
        jobsPosted: user.postedJobs?.length || 0,
        jobsTaken: user.takenJobs?.length || 0,
        rating: user.rating || 0,
      };
    } catch (error) {
      console.error('Error fetching user stats:', error);
      throw new Error('Failed to fetch user stats');
    }
  }
}
