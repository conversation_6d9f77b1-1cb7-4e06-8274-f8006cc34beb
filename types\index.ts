export type Job = {
  id: string;
  title: string;
  description: string;
  reward: number;
  location: string;
  status: 'open' | 'in-progress' | 'completed';
  postedBy: string;
  postedDate: string;
  takenBy?: string;
  category: string;
  createdAt?: any; // Firebase Timestamp
  updatedAt?: any; // Firebase Timestamp
};

export type User = {
  id: string;
  name: string;
  email: string;
  postedJobs: string[]; // Array of job IDs
  takenJobs: string[]; // Array of job IDs
  rating: number;
  createdAt?: any; // Firebase Timestamp
  updatedAt?: any; // Firebase Timestamp
};

export type LoadingState = {
  loading: boolean;
  error: string | null;
};

export type JobsState = LoadingState & {
  jobs: Job[];
};

export type UserState = LoadingState & {
  user: User | null;
};