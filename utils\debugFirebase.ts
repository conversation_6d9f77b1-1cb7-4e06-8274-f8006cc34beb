import { db } from '@/config/firebase';
import { collection, getDocs, addDoc, serverTimestamp } from 'firebase/firestore';

// Debug function to test Firebase connection
export async function testFirebaseConnection() {
  try {
    console.log('🔥 Testing Firebase connection...');
    
    // Test 1: Try to read from jobs collection
    const jobsRef = collection(db, 'jobs');
    const snapshot = await getDocs(jobsRef);
    
    console.log('✅ Firebase connection successful!');
    console.log(`📊 Found ${snapshot.size} jobs in database`);
    
    if (snapshot.size === 0) {
      console.log('📝 No jobs found. Creating sample job...');
      await createSampleJob();
    } else {
      console.log('📋 Existing jobs:');
      snapshot.forEach((doc) => {
        console.log(`- ${doc.id}:`, doc.data());
      });
    }
    
    return true;
  } catch (error) {
    console.error('❌ Firebase connection failed:', error);
    return false;
  }
}

// Create a sample job for testing
export async function createSampleJob() {
  try {
    const jobsRef = collection(db, 'jobs');
    const sampleJob = {
      title: 'Test Job - House Cleaning',
      description: 'This is a test job created automatically to verify Firebase connection.',
      reward: 100,
      location: 'Central District',
      status: 'open',
      postedBy: 'demo-user-123',
      category: 'Cleaning',
      postedDate: '2024-02-20',
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    };
    
    const docRef = await addDoc(jobsRef, sampleJob);
    console.log('✅ Sample job created with ID:', docRef.id);
    
    return docRef.id;
  } catch (error) {
    console.error('❌ Failed to create sample job:', error);
    throw error;
  }
}

// Create sample user
export async function createSampleUser() {
  try {
    const usersRef = collection(db, 'users');
    const sampleUser = {
      id: 'demo-user-123',
      name: 'John Doe',
      email: '<EMAIL>',
      rating: 4.5,
      postedJobs: [],
      takenJobs: [],
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    };
    
    const docRef = await addDoc(usersRef, sampleUser);
    console.log('✅ Sample user created with ID:', docRef.id);
    
    return docRef.id;
  } catch (error) {
    console.error('❌ Failed to create sample user:', error);
    throw error;
  }
}
